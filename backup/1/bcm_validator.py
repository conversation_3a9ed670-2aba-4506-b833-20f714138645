"""
BCM Validator - Main validation engine for ISO 22301 compliance
Validates BCM documents against ISO 22301:2019 standards and generates compliance reports.
"""

import re
import json
from typing import Dict, <PERSON>, Tu<PERSON>, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from iso_22301_standards import ISO22301Standards, ComplianceLevel, ISO22301Requirement
from document_analyzer import DocumentAnalyzer, DocumentStructure

@dataclass
class ValidationResult:
    """Result of validating a single requirement"""
    clause: str
    title: str
    compliance_level: ComplianceLevel
    score: float  # 0-100
    findings: List[str]
    evidence: List[str]
    recommendations: List[str]
    gaps: List[str]

@dataclass
class ComplianceReport:
    """Complete compliance report"""
    document_title: str
    validation_date: datetime
    overall_score: float
    compliance_summary: Dict[str, int]  # Count by compliance level
    validation_results: List[ValidationResult]
    mandatory_documents_check: Dict[str, bool]
    recommendations: List[str]
    critical_gaps: List[str]
    document_summary: Dict[str, Any]

class BCMValidator:
    """Main BCM validation engine"""
    
    def __init__(self):
        self.standards = ISO22301Standards()
        self.analyzer = DocumentAnalyzer()
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging for the validator"""
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def validate_document(self, file_path: str) -> ComplianceReport:
        """Validate a BCM document against ISO 22301 standards"""
        self.logger.info(f"Starting validation of: {file_path}")
        
        # Analyze document structure
        document_structure = self.analyzer.analyze_document_structure(file_path)
        
        # Validate against each requirement
        validation_results = []
        for clause, requirement in self.standards.get_all_requirements().items():
            result = self.validate_requirement(document_structure, requirement)
            validation_results.append(result)
        
        # Check mandatory documents
        mandatory_docs_check = self.check_mandatory_documents(document_structure)
        
        # Calculate overall compliance
        compliance_levels = [result.compliance_level for result in validation_results]
        compliance_summary = {
            ComplianceLevel.FULLY_COMPLIANT.value: compliance_levels.count(ComplianceLevel.FULLY_COMPLIANT),
            ComplianceLevel.PARTIALLY_COMPLIANT.value: compliance_levels.count(ComplianceLevel.PARTIALLY_COMPLIANT),
            ComplianceLevel.NON_COMPLIANT.value: compliance_levels.count(ComplianceLevel.NON_COMPLIANT),
            ComplianceLevel.NOT_APPLICABLE.value: compliance_levels.count(ComplianceLevel.NOT_APPLICABLE)
        }
        
        # Calculate overall score
        compliance_dict = {result.clause: result.compliance_level for result in validation_results}
        overall_score = self.standards.calculate_compliance_score(compliance_dict)
        
        # Generate recommendations and identify critical gaps
        recommendations = self.generate_recommendations(validation_results)
        critical_gaps = self.identify_critical_gaps(validation_results)
        
        # Create compliance report
        report = ComplianceReport(
            document_title=document_structure.title,
            validation_date=datetime.now(),
            overall_score=overall_score,
            compliance_summary=compliance_summary,
            validation_results=validation_results,
            mandatory_documents_check=mandatory_docs_check,
            recommendations=recommendations,
            critical_gaps=critical_gaps,
            document_summary=self.analyzer.get_document_summary(document_structure)
        )
        
        self.logger.info(f"Validation complete. Overall score: {overall_score:.1f}%")
        return report
    
    def validate_requirement(self, document_structure: DocumentStructure, requirement: ISO22301Requirement) -> ValidationResult:
        """Validate a single ISO 22301 requirement"""
        findings = []
        evidence = []
        gaps = []
        recommendations = []
        
        # Search for requirement keywords in document
        keyword_matches = self.analyzer.search_content(
            document_structure, 
            requirement.validation_criteria.keywords
        )
        
        # Check for required sections
        section_matches = self.check_required_sections(
            document_structure, 
            requirement.validation_criteria.required_sections
        )
        
        # Analyze content for compliance indicators
        compliance_indicators = self.analyze_compliance_indicators(
            document_structure, 
            requirement
        )
        
        # Determine compliance level
        compliance_level, score = self.determine_compliance_level(
            keyword_matches, 
            section_matches, 
            compliance_indicators,
            requirement
        )
        
        # Generate findings
        if keyword_matches:
            findings.append(f"Found {len(keyword_matches)} keyword matches")
            for keyword, matches in keyword_matches.items():
                evidence.extend([f"'{keyword}' found in {match[0]}" for match in matches[:3]])
        
        if section_matches:
            findings.append(f"Found {len(section_matches)} relevant sections")
            evidence.extend([f"Section: {section}" for section in section_matches[:5]])
        
        # Identify gaps
        missing_keywords = [kw for kw in requirement.validation_criteria.keywords if kw not in keyword_matches]
        missing_sections = [sec for sec in requirement.validation_criteria.required_sections if sec not in section_matches]
        
        if missing_keywords:
            gaps.append(f"Missing keywords: {', '.join(missing_keywords[:5])}")
        
        if missing_sections:
            gaps.append(f"Missing sections: {', '.join(missing_sections)}")
        
        # Generate recommendations
        if compliance_level in [ComplianceLevel.NON_COMPLIANT, ComplianceLevel.PARTIALLY_COMPLIANT]:
            recommendations.extend(self.generate_requirement_recommendations(requirement, gaps))
        
        return ValidationResult(
            clause=requirement.clause,
            title=requirement.title,
            compliance_level=compliance_level,
            score=score,
            findings=findings,
            evidence=evidence,
            recommendations=recommendations,
            gaps=gaps
        )
    
    def check_required_sections(self, document_structure: DocumentStructure, required_sections: List[str]) -> List[str]:
        """Check if required sections are present in the document"""
        found_sections = []
        
        for required_section in required_sections:
            for section in document_structure.sections:
                # Fuzzy matching for section titles
                if self.fuzzy_match(required_section.lower(), section.title.lower()):
                    found_sections.append(section.title)
                    break
        
        return found_sections
    
    def fuzzy_match(self, required: str, actual: str, threshold: float = 0.6) -> bool:
        """Fuzzy matching for section titles"""
        required_words = set(required.split())
        actual_words = set(actual.split())
        
        if not required_words:
            return False
        
        # Calculate Jaccard similarity
        intersection = required_words.intersection(actual_words)
        union = required_words.union(actual_words)
        
        similarity = len(intersection) / len(union) if union else 0
        return similarity >= threshold
    
    def analyze_compliance_indicators(self, document_structure: DocumentStructure, requirement: ISO22301Requirement) -> Dict[str, Any]:
        """Analyze specific compliance indicators for a requirement"""
        indicators = {}
        
        # Extract compliance indicators from document
        compliance_data = self.analyzer.extract_compliance_indicators(document_structure)
        
        # Map requirement to relevant indicators
        clause_mapping = {
            '4.2.2': ['iso_22301_references'],
            '5.2': ['policy_statements'],
            '7.2': ['training_references'],
            '8.2': ['risk_assessments', 'business_impact_analysis'],
            '8.5': ['testing_references'],
            '9.2': ['review_references'],
            '9.3': ['review_references']
        }
        
        relevant_indicators = clause_mapping.get(requirement.clause, [])
        
        for indicator in relevant_indicators:
            if indicator in compliance_data:
                indicators[indicator] = len(compliance_data[indicator])
        
        return indicators
    
    def determine_compliance_level(self, keyword_matches: Dict[str, List], section_matches: List[str], 
                                 compliance_indicators: Dict[str, Any], requirement: ISO22301Requirement) -> Tuple[ComplianceLevel, float]:
        """Determine compliance level and score for a requirement"""
        
        # Calculate keyword coverage
        keyword_coverage = len(keyword_matches) / len(requirement.validation_criteria.keywords) if requirement.validation_criteria.keywords else 0
        
        # Calculate section coverage
        section_coverage = len(section_matches) / len(requirement.validation_criteria.required_sections) if requirement.validation_criteria.required_sections else 1
        
        # Calculate indicator score
        indicator_score = min(1.0, sum(compliance_indicators.values()) / 3) if compliance_indicators else 0
        
        # Weighted average
        overall_score = (keyword_coverage * 0.4 + section_coverage * 0.4 + indicator_score * 0.2) * 100
        
        # Determine compliance level
        if overall_score >= 80:
            compliance_level = ComplianceLevel.FULLY_COMPLIANT
        elif overall_score >= 50:
            compliance_level = ComplianceLevel.PARTIALLY_COMPLIANT
        elif overall_score > 0:
            compliance_level = ComplianceLevel.NON_COMPLIANT
        else:
            # Check if requirement might not be applicable
            if not requirement.mandatory or self.is_requirement_not_applicable(requirement):
                compliance_level = ComplianceLevel.NOT_APPLICABLE
            else:
                compliance_level = ComplianceLevel.NON_COMPLIANT
        
        return compliance_level, overall_score
    
    def is_requirement_not_applicable(self, requirement: ISO22301Requirement) -> bool:
        """Determine if a requirement might not be applicable"""
        # This is a simplified check - in practice, this would be more sophisticated
        na_indicators = ['specific industry', 'large organizations', 'complex systems']
        return any(indicator in requirement.description.lower() for indicator in na_indicators)
    
    def check_mandatory_documents(self, document_structure: DocumentStructure) -> Dict[str, bool]:
        """Check if mandatory documents are present or referenced"""
        mandatory_docs = self.standards.get_mandatory_documents()
        results = {}
        
        for doc in mandatory_docs:
            # Extract key terms from document description
            key_terms = self.extract_key_terms(doc)
            
            # Search for these terms in the document
            found = False
            for section in document_structure.sections:
                content_lower = section.content.lower()
                if any(term.lower() in content_lower for term in key_terms):
                    found = True
                    break
            
            results[doc] = found
        
        return results
    
    def extract_key_terms(self, document_description: str) -> List[str]:
        """Extract key terms from document description"""
        # Remove clause references
        clean_desc = re.sub(r'\(\d+\.\d+\.?\d*\)', '', document_description)
        
        # Split into words and filter
        words = re.findall(r'\b[a-zA-Z]{3,}\b', clean_desc)
        
        # Remove common words
        stop_words = {'list', 'results', 'nature', 'data', 'records', 'information'}
        key_terms = [word for word in words if word.lower() not in stop_words]
        
        return key_terms
    
    def generate_recommendations(self, validation_results: List[ValidationResult]) -> List[str]:
        """Generate overall recommendations based on validation results"""
        recommendations = []
        
        # Count compliance levels
        non_compliant = [r for r in validation_results if r.compliance_level == ComplianceLevel.NON_COMPLIANT]
        partially_compliant = [r for r in validation_results if r.compliance_level == ComplianceLevel.PARTIALLY_COMPLIANT]
        
        if len(non_compliant) > 5:
            recommendations.append("Consider comprehensive BCMS implementation training for the team")
            recommendations.append("Develop a structured approach to address multiple compliance gaps")
        
        if len(partially_compliant) > 3:
            recommendations.append("Review and enhance existing procedures to achieve full compliance")
            recommendations.append("Implement regular internal audits to monitor compliance status")
        
        # Specific recommendations based on common gaps
        common_gaps = {}
        for result in validation_results:
            for gap in result.gaps:
                common_gaps[gap] = common_gaps.get(gap, 0) + 1
        
        # Add recommendations for most common gaps
        for gap, count in sorted(common_gaps.items(), key=lambda x: x[1], reverse=True)[:3]:
            if count > 2:
                recommendations.append(f"Address common gap: {gap}")
        
        return recommendations
    
    def identify_critical_gaps(self, validation_results: List[ValidationResult]) -> List[str]:
        """Identify critical gaps that need immediate attention"""
        critical_gaps = []
        
        # High-priority clauses that are critical for BCMS
        critical_clauses = ['4.3', '5.2', '6.2', '8.2', '8.4', '9.2']
        
        for result in validation_results:
            if (result.clause in critical_clauses and 
                result.compliance_level == ComplianceLevel.NON_COMPLIANT):
                critical_gaps.append(f"Critical gap in {result.clause}: {result.title}")
        
        return critical_gaps
    
    def generate_requirement_recommendations(self, requirement: ISO22301Requirement, gaps: List[str]) -> List[str]:
        """Generate specific recommendations for a requirement"""
        recommendations = []
        
        if gaps:
            recommendations.append(f"Develop documentation addressing: {requirement.title}")
            
            # Specific recommendations based on clause
            clause_recommendations = {
                '4.3': ["Define clear BCMS scope and boundaries", "Document exclusions with justifications"],
                '5.2': ["Establish comprehensive BC policy", "Ensure top management commitment"],
                '6.2': ["Set measurable BC objectives", "Develop implementation plans"],
                '8.2': ["Conduct thorough business impact analysis", "Perform comprehensive risk assessment"],
                '8.4': ["Develop detailed BC plans and procedures", "Include response and recovery procedures"],
                '8.5': ["Establish regular testing program", "Document exercise results and improvements"],
                '9.2': ["Implement internal audit program", "Train internal auditors"],
                '9.3': ["Schedule regular management reviews", "Document review outcomes and decisions"]
            }
            
            if requirement.clause in clause_recommendations:
                recommendations.extend(clause_recommendations[requirement.clause])
        
        return recommendations
    
    def export_report(self, report: ComplianceReport, output_format: str = 'json') -> str:
        """Export compliance report in specified format"""
        if output_format.lower() == 'json':
            return json.dumps(asdict(report), indent=2, default=str)
        elif output_format.lower() == 'html':
            return self.generate_html_report(report)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
    
    def generate_html_report(self, report: ComplianceReport) -> str:
        """Generate HTML compliance report"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>BCM Compliance Report - {report.document_title}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .score {{ font-size: 24px; font-weight: bold; color: #2c5aa0; }}
                .section {{ margin: 20px 0; }}
                .requirement {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
                .compliant {{ border-left: 5px solid #28a745; }}
                .partial {{ border-left: 5px solid #ffc107; }}
                .non-compliant {{ border-left: 5px solid #dc3545; }}
                .not-applicable {{ border-left: 5px solid #6c757d; }}
                .recommendations {{ background-color: #e7f3ff; padding: 15px; border-radius: 5px; }}
                .critical-gaps {{ background-color: #ffe6e6; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>BCM Compliance Report</h1>
                <h2>{report.document_title}</h2>
                <p>Validation Date: {report.validation_date.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <div class="score">Overall Compliance Score: {report.overall_score:.1f}%</div>
            </div>
            
            <div class="section">
                <h3>Compliance Summary</h3>
                <ul>
                    <li>Fully Compliant: {report.compliance_summary[ComplianceLevel.FULLY_COMPLIANT.value]}</li>
                    <li>Partially Compliant: {report.compliance_summary[ComplianceLevel.PARTIALLY_COMPLIANT.value]}</li>
                    <li>Non-Compliant: {report.compliance_summary[ComplianceLevel.NON_COMPLIANT.value]}</li>
                    <li>Not Applicable: {report.compliance_summary[ComplianceLevel.NOT_APPLICABLE.value]}</li>
                </ul>
            </div>
        """
        
        # Add critical gaps
        if report.critical_gaps:
            html += """
            <div class="section">
                <h3>Critical Gaps</h3>
                <div class="critical-gaps">
                    <ul>
            """
            for gap in report.critical_gaps:
                html += f"<li>{gap}</li>"
            html += """
                    </ul>
                </div>
            </div>
            """
        
        # Add recommendations
        if report.recommendations:
            html += """
            <div class="section">
                <h3>Recommendations</h3>
                <div class="recommendations">
                    <ul>
            """
            for rec in report.recommendations:
                html += f"<li>{rec}</li>"
            html += """
                    </ul>
                </div>
            </div>
            """
        
        # Add detailed results
        html += """
            <div class="section">
                <h3>Detailed Validation Results</h3>
        """
        
        for result in report.validation_results:
            css_class = {
                ComplianceLevel.FULLY_COMPLIANT: 'compliant',
                ComplianceLevel.PARTIALLY_COMPLIANT: 'partial',
                ComplianceLevel.NON_COMPLIANT: 'non-compliant',
                ComplianceLevel.NOT_APPLICABLE: 'not-applicable'
            }[result.compliance_level]
            
            html += f"""
                <div class="requirement {css_class}">
                    <h4>{result.clause}: {result.title}</h4>
                    <p><strong>Compliance Level:</strong> {result.compliance_level.value}</p>
                    <p><strong>Score:</strong> {result.score:.1f}%</p>
            """
            
            if result.findings:
                html += "<p><strong>Findings:</strong></p><ul>"
                for finding in result.findings:
                    html += f"<li>{finding}</li>"
                html += "</ul>"
            
            if result.gaps:
                html += "<p><strong>Gaps:</strong></p><ul>"
                for gap in result.gaps:
                    html += f"<li>{gap}</li>"
                html += "</ul>"
            
            if result.recommendations:
                html += "<p><strong>Recommendations:</strong></p><ul>"
                for rec in result.recommendations:
                    html += f"<li>{rec}</li>"
                html += "</ul>"
            
            html += "</div>"
        
        html += """
            </div>
        </body>
        </html>
        """
        
        return html

"""
BCM Expert - Main application for BCM document validation against ISO 22301 standards
Combines document analysis, validation, and reporting capabilities.
"""

import os
import sys
import argparse
from typing import Optional
import logging
from datetime import datetime

from bcm_validator import BCMValidator
from validation_report import ValidationReportGenerator
from document_analyzer import DocumentAnalyzer
from iso_22301_standards import ISO22301Standards

class BCMExpert:
    """Main BCM Expert application"""
    
    def __init__(self):
        self.validator = BCMValidator()
        self.report_generator = ValidationReportGenerator()
        self.analyzer = DocumentAnalyzer()
        self.standards = ISO22301Standards()
        self.setup_logging()
    
    def setup_logging(self):
        """Setup application logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('bcm_expert.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def validate_document(self, file_path: str, output_dir: str = '.') -> dict:
        """Validate a BCM document and generate comprehensive reports"""
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Document not found: {file_path}")
        
        self.logger.info(f"Starting BCM validation for: {file_path}")
        
        try:
            # Validate document
            compliance_report = self.validator.validate_document(file_path)
            
            # Generate comprehensive reports
            report_outputs = self.report_generator.generate_comprehensive_report(
                compliance_report, output_dir
            )
            
            # Generate HTML report
            html_report = self.validator.generate_html_report(compliance_report)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            html_filename = f'{output_dir}/compliance_report_{timestamp}.html'
            
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_report)
            
            report_outputs['html'] = html_filename
            
            self.logger.info(f"Validation complete. Overall score: {compliance_report.overall_score:.1f}%")
            
            return {
                'compliance_report': compliance_report,
                'output_files': report_outputs,
                'summary': {
                    'overall_score': compliance_report.overall_score,
                    'total_requirements': len(compliance_report.validation_results),
                    'critical_gaps': len(compliance_report.critical_gaps),
                    'recommendations': len(compliance_report.recommendations)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error during validation: {e}")
            raise
    
    def analyze_document_structure(self, file_path: str) -> dict:
        """Analyze document structure without full validation"""
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Document not found: {file_path}")
        
        self.logger.info(f"Analyzing document structure: {file_path}")
        
        try:
            document_structure = self.analyzer.analyze_document_structure(file_path)
            summary = self.analyzer.get_document_summary(document_structure)
            compliance_indicators = self.analyzer.extract_compliance_indicators(document_structure)
            
            return {
                'document_structure': document_structure,
                'summary': summary,
                'compliance_indicators': compliance_indicators
            }
            
        except Exception as e:
            self.logger.error(f"Error during document analysis: {e}")
            raise
    
    def get_iso_standards_info(self) -> dict:
        """Get information about ISO 22301 standards"""
        
        return {
            'total_requirements': len(self.standards.get_all_requirements()),
            'mandatory_requirements': len(self.standards.get_mandatory_requirements()),
            'mandatory_documents': self.standards.get_mandatory_documents(),
            'clause_groups': {
                '4': 'Context of the Organization',
                '5': 'Leadership',
                '6': 'Planning',
                '7': 'Support',
                '8': 'Operation',
                '9': 'Performance Evaluation',
                '10': 'Improvement'
            },
            'validation_keywords': self.standards.get_validation_keywords()
        }
    
    def interactive_mode(self):
        """Run in interactive mode for document validation"""
        
        print("🏢 BCM Expert - ISO 22301 Document Validator")
        print("=" * 50)
        print()
        
        while True:
            print("\nOptions:")
            print("1. Validate BCM Document")
            print("2. Analyze Document Structure")
            print("3. View ISO 22301 Standards Info")
            print("4. Exit")
            
            choice = input("\nSelect option (1-4): ").strip()
            
            if choice == '1':
                self.interactive_validate()
            elif choice == '2':
                self.interactive_analyze()
            elif choice == '3':
                self.interactive_standards_info()
            elif choice == '4':
                print("👋 Thank you for using BCM Expert!")
                break
            else:
                print("❌ Invalid option. Please try again.")
    
    def interactive_validate(self):
        """Interactive document validation"""
        
        file_path = input("\n📄 Enter path to BCM document (PDF): ").strip()
        
        if not file_path:
            print("❌ No file path provided.")
            return
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return
        
        output_dir = input("📁 Enter output directory (press Enter for current): ").strip()
        if not output_dir:
            output_dir = '.'
        
        try:
            print("\n🔍 Validating document...")
            result = self.validate_document(file_path, output_dir)
            
            print("\n✅ Validation Complete!")
            print(f"Overall Compliance Score: {result['summary']['overall_score']:.1f}%")
            print(f"Total Requirements: {result['summary']['total_requirements']}")
            print(f"Critical Gaps: {result['summary']['critical_gaps']}")
            print(f"Recommendations: {result['summary']['recommendations']}")
            
            print("\n📊 Generated Reports:")
            for report_type, filename in result['output_files'].items():
                print(f"  • {report_type.upper()}: {filename}")
            
        except Exception as e:
            print(f"❌ Error during validation: {e}")
    
    def interactive_analyze(self):
        """Interactive document analysis"""
        
        file_path = input("\n📄 Enter path to document (PDF): ").strip()
        
        if not file_path:
            print("❌ No file path provided.")
            return
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return
        
        try:
            print("\n🔍 Analyzing document structure...")
            result = self.analyze_document_structure(file_path)
            
            summary = result['summary']
            print("\n📋 Document Summary:")
            print(f"  Title: {summary['title']}")
            print(f"  Pages: {summary['total_pages']}")
            print(f"  Word Count: {summary['word_count']}")
            print(f"  Sections: {summary['section_count']}")
            
            print("\n🔑 Top Keywords:")
            for keyword in summary['top_keywords'][:10]:
                print(f"  • {keyword}")
            
            indicators = result['compliance_indicators']
            print("\n📊 Compliance Indicators Found:")
            for indicator, items in indicators.items():
                if items:
                    print(f"  • {indicator.replace('_', ' ').title()}: {len(items)} references")
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
    
    def interactive_standards_info(self):
        """Display ISO 22301 standards information"""
        
        info = self.get_iso_standards_info()
        
        print("\n📋 ISO 22301:2019 Standards Information")
        print("=" * 40)
        print(f"Total Requirements: {info['total_requirements']}")
        print(f"Mandatory Requirements: {info['mandatory_requirements']}")
        
        print("\n📚 Clause Groups:")
        for clause, title in info['clause_groups'].items():
            requirements_count = len(self.standards.get_requirements_by_clause_group(clause))
            print(f"  Clause {clause}: {title} ({requirements_count} requirements)")
        
        print(f"\n📄 Mandatory Documents: {len(info['mandatory_documents'])}")
        for doc in info['mandatory_documents'][:5]:
            print(f"  • {doc}")
        
        if len(info['mandatory_documents']) > 5:
            print(f"  ... and {len(info['mandatory_documents']) - 5} more")
        
        print(f"\n🔍 Validation Keywords: {len(info['validation_keywords'])}")
        print("Sample keywords:", ", ".join(info['validation_keywords'][:10]))

def main():
    """Main entry point"""
    
    parser = argparse.ArgumentParser(description='BCM Expert - ISO 22301 Document Validator')
    parser.add_argument('--file', '-f', help='Path to BCM document (PDF)')
    parser.add_argument('--output', '-o', default='.', help='Output directory for reports')
    parser.add_argument('--interactive', '-i', action='store_true', help='Run in interactive mode')
    parser.add_argument('--analyze-only', '-a', action='store_true', help='Only analyze document structure')
    
    args = parser.parse_args()
    
    bcm_expert = BCMExpert()
    
    if args.interactive or not args.file:
        bcm_expert.interactive_mode()
    else:
        try:
            if args.analyze_only:
                print(f"🔍 Analyzing document: {args.file}")
                result = bcm_expert.analyze_document_structure(args.file)
                
                summary = result['summary']
                print(f"\n📋 Document: {summary['title']}")
                print(f"Pages: {summary['total_pages']}, Words: {summary['word_count']}, Sections: {summary['section_count']}")
                
            else:
                print(f"🔍 Validating document: {args.file}")
                result = bcm_expert.validate_document(args.file, args.output)
                
                print(f"\n✅ Validation Complete!")
                print(f"Overall Compliance Score: {result['summary']['overall_score']:.1f}%")
                print(f"Reports generated in: {args.output}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()

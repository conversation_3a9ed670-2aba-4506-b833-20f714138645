"""
Document Analyzer for BCM Documents
Analyzes document structure, content, and extracts relevant information for ISO 22301 validation.
"""

import re
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from pypdf import PdfReader
    except ImportError:
        print("Warning: Neither PyPDF2 nor pypdf found. PDF processing will not work.")
        PdfReader = None
from collections import Counter
import logging

# Optional imports
try:
    import nltk
    # Download required NLTK data
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt', quiet=True)

    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('stopwords', quiet=True)
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

@dataclass
class DocumentSection:
    """Represents a section of the document"""
    title: str
    content: str
    level: int  # Heading level (1, 2, 3, etc.)
    page_number: int
    keywords: List[str]

@dataclass
class DocumentStructure:
    """Represents the overall document structure"""
    title: str
    sections: List[DocumentSection]
    total_pages: int
    word_count: int
    table_of_contents: List[str]
    metadata: Dict[str, Any]

class DocumentAnalyzer:
    """Analyzes BCM documents for structure and content"""

    def __init__(self):
        self.setup_logging()
        self.load_nlp_models()

    def setup_logging(self):
        """Setup logging for the analyzer"""
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def load_nlp_models(self):
        """Load NLP models for text processing"""
        if SPACY_AVAILABLE:
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                self.logger.warning("spaCy model 'en_core_web_sm' not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None
        else:
            self.logger.warning("spaCy not available. Some NLP features will be limited.")
            self.nlp = None

    def extract_text_from_pdf(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Extract text and metadata from PDF"""
        if PdfReader is None:
            raise ImportError("PDF reader not available. Please install PyPDF2 or pypdf.")

        try:
            reader = PdfReader(file_path)
            text = ""
            metadata = {}

            # Extract metadata
            if reader.metadata:
                metadata = {
                    'title': reader.metadata.get('/Title', ''),
                    'author': reader.metadata.get('/Author', ''),
                    'subject': reader.metadata.get('/Subject', ''),
                    'creator': reader.metadata.get('/Creator', ''),
                    'creation_date': reader.metadata.get('/CreationDate', ''),
                    'modification_date': reader.metadata.get('/ModDate', '')
                }

            # Extract text from all pages
            for page_num, page in enumerate(reader.pages, 1):
                page_text = page.extract_text() or ""
                text += f"\n--- PAGE {page_num} ---\n{page_text}"

            metadata['total_pages'] = len(reader.pages)
            metadata['word_count'] = len(text.split())

            return text, metadata

        except Exception as e:
            self.logger.error(f"Error extracting text from PDF: {e}")
            return "", {}

    def identify_sections(self, text: str) -> List[DocumentSection]:
        """Identify document sections based on headings and structure"""
        sections = []
        lines = text.split('\n')
        current_section = None
        current_content = []
        page_number = 1

        # Patterns for identifying headings
        heading_patterns = [
            r'^(\d+\.?\d*\.?\d*)\s+([A-Z][^a-z]*[A-Z].*)',  # Numbered headings
            r'^([A-Z][A-Z\s]+)$',  # ALL CAPS headings
            r'^([A-Z][a-zA-Z\s]+):?\s*$',  # Title case headings
            r'^(CHAPTER|SECTION|PART)\s+(\d+)',  # Chapter/Section headings
        ]

        for line in lines:
            line = line.strip()

            # Track page numbers
            if line.startswith('--- PAGE'):
                page_match = re.search(r'PAGE (\d+)', line)
                if page_match:
                    page_number = int(page_match.group(1))
                continue

            # Skip empty lines
            if not line:
                continue

            # Check if line is a heading
            is_heading = False
            heading_level = 1
            heading_title = line

            for pattern in heading_patterns:
                match = re.match(pattern, line)
                if match:
                    is_heading = True
                    if len(match.groups()) > 1:
                        heading_title = match.group(2).strip()
                    else:
                        heading_title = match.group(1).strip()

                    # Determine heading level based on numbering
                    if '.' in match.group(1):
                        heading_level = match.group(1).count('.') + 1
                    break

            if is_heading:
                # Save previous section
                if current_section and current_content:
                    current_section.content = '\n'.join(current_content)
                    current_section.keywords = self.extract_keywords(current_section.content)
                    sections.append(current_section)

                # Start new section
                current_section = DocumentSection(
                    title=heading_title,
                    content="",
                    level=heading_level,
                    page_number=page_number,
                    keywords=[]
                )
                current_content = []
            else:
                # Add to current section content
                if current_section:
                    current_content.append(line)
                else:
                    # Create a default section for content before first heading
                    current_section = DocumentSection(
                        title="Introduction",
                        content="",
                        level=1,
                        page_number=page_number,
                        keywords=[]
                    )
                    current_content = [line]

        # Add final section
        if current_section and current_content:
            current_section.content = '\n'.join(current_content)
            current_section.keywords = self.extract_keywords(current_section.content)
            sections.append(current_section)

        return sections

    def extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text using NLP"""
        if not text:
            return []

        keywords = []

        # Simple keyword extraction using word frequency
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())

        # Remove common stop words
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'shall', 'have', 'has', 'had', 'been', 'being', 'are', 'was', 'were', 'is', 'am'}

        filtered_words = [word for word in words if word not in stop_words and len(word) > 3]

        # Get most common words
        word_freq = Counter(filtered_words)
        keywords = [word for word, freq in word_freq.most_common(20)]

        # Use spaCy for better keyword extraction if available
        if self.nlp:
            try:
                doc = self.nlp(text[:1000000])  # Limit text length for processing

                # Extract named entities and important terms
                entities = [ent.text.lower() for ent in doc.ents if ent.label_ in ['ORG', 'PRODUCT', 'EVENT', 'LAW']]

                # Extract noun phrases
                noun_phrases = [chunk.text.lower() for chunk in doc.noun_chunks if len(chunk.text.split()) <= 3]

                keywords.extend(entities)
                keywords.extend(noun_phrases)

            except Exception as e:
                self.logger.warning(f"Error in spaCy processing: {e}")

        # Remove duplicates and return
        return list(set(keywords))

    def generate_table_of_contents(self, sections: List[DocumentSection]) -> List[str]:
        """Generate table of contents from sections"""
        toc = []
        for section in sections:
            indent = "  " * (section.level - 1)
            toc.append(f"{indent}{section.title} (Page {section.page_number})")
        return toc

    def analyze_document_structure(self, file_path: str) -> DocumentStructure:
        """Analyze complete document structure"""
        self.logger.info(f"Analyzing document: {file_path}")

        # Extract text and metadata
        text, metadata = self.extract_text_from_pdf(file_path)

        if not text:
            raise ValueError("Could not extract text from document")

        # Identify sections
        sections = self.identify_sections(text)

        # Generate table of contents
        toc = self.generate_table_of_contents(sections)

        # Create document structure
        document_structure = DocumentStructure(
            title=metadata.get('title', 'Unknown Document'),
            sections=sections,
            total_pages=metadata.get('total_pages', 0),
            word_count=metadata.get('word_count', 0),
            table_of_contents=toc,
            metadata=metadata
        )

        self.logger.info(f"Document analysis complete: {len(sections)} sections found")
        return document_structure

    def search_content(self, document_structure: DocumentStructure, keywords: List[str]) -> Dict[str, List[Tuple[str, str]]]:
        """Search for keywords in document content"""
        results = {}

        for keyword in keywords:
            matches = []
            keyword_lower = keyword.lower()

            for section in document_structure.sections:
                content_lower = section.content.lower()

                # Find all occurrences
                if keyword_lower in content_lower:
                    # Extract context around the keyword
                    sentences = re.split(r'[.!?]+', section.content)
                    for sentence in sentences:
                        if keyword_lower in sentence.lower():
                            matches.append((section.title, sentence.strip()))

            if matches:
                results[keyword] = matches

        return results

    def extract_compliance_indicators(self, document_structure: DocumentStructure) -> Dict[str, Any]:
        """Extract indicators of compliance with various standards"""
        indicators = {
            'iso_22301_references': [],
            'policy_statements': [],
            'procedure_references': [],
            'risk_assessments': [],
            'business_impact_analysis': [],
            'testing_references': [],
            'training_references': [],
            'review_references': []
        }

        # Define search patterns
        patterns = {
            'iso_22301_references': [r'iso\s*22301', r'business\s+continuity\s+management\s+system', r'bcms'],
            'policy_statements': [r'policy', r'commitment', r'statement'],
            'procedure_references': [r'procedure', r'process', r'workflow'],
            'risk_assessments': [r'risk\s+assessment', r'risk\s+analysis', r'threat\s+analysis'],
            'business_impact_analysis': [r'business\s+impact\s+analysis', r'bia', r'impact\s+assessment'],
            'testing_references': [r'test', r'exercise', r'drill', r'simulation'],
            'training_references': [r'training', r'awareness', r'education', r'competence'],
            'review_references': [r'review', r'audit', r'assessment', r'evaluation']
        }

        # Search for patterns in each section
        for section in document_structure.sections:
            content_lower = section.content.lower()

            for category, pattern_list in patterns.items():
                for pattern in pattern_list:
                    matches = re.finditer(pattern, content_lower)
                    for match in matches:
                        # Extract context
                        start = max(0, match.start() - 100)
                        end = min(len(section.content), match.end() + 100)
                        context = section.content[start:end].strip()

                        indicators[category].append({
                            'section': section.title,
                            'match': match.group(),
                            'context': context,
                            'page': section.page_number
                        })

        return indicators

    def get_document_summary(self, document_structure: DocumentStructure) -> Dict[str, Any]:
        """Generate a summary of the document"""
        summary = {
            'title': document_structure.title,
            'total_pages': document_structure.total_pages,
            'word_count': document_structure.word_count,
            'section_count': len(document_structure.sections),
            'sections': [{'title': s.title, 'level': s.level, 'page': s.page_number} for s in document_structure.sections],
            'top_keywords': [],
            'metadata': document_structure.metadata
        }

        # Aggregate keywords from all sections
        all_keywords = []
        for section in document_structure.sections:
            all_keywords.extend(section.keywords)

        # Get top keywords
        keyword_freq = Counter(all_keywords)
        summary['top_keywords'] = [word for word, freq in keyword_freq.most_common(20)]

        return summary

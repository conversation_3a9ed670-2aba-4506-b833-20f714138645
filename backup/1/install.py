#!/usr/bin/env python3
"""
Installation and setup script for BCM Expert
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install Python requirements"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )

def install_spacy_model():
    """Install spaCy English model"""
    return run_command(
        f"{sys.executable} -m spacy download en_core_web_sm",
        "Installing spaCy English model"
    )

def download_nltk_data():
    """Download required NLTK data"""
    try:
        import nltk
        print("🔄 Downloading NLTK data...")
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        print("✅ NLTK data downloaded successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to download NLTK data: {e}")
        return False

def check_ollama():
    """Check if Ollama is installed and suggest models"""
    try:
        result = subprocess.run("ollama --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama is installed")
            print("\n📋 Recommended Ollama models for BCM Expert:")
            print("  • llama2 (for general LLM functionality)")
            print("  • nomic-embed-text (for embeddings)")
            print("\n🔄 To install these models, run:")
            print("  ollama pull llama2")
            print("  ollama pull nomic-embed-text")
            return True
        else:
            print("⚠️  Ollama not found")
            print("📋 Ollama is optional but recommended for full functionality")
            print("🔗 Install from: https://ollama.ai")
            return False
    except FileNotFoundError:
        print("⚠️  Ollama not found")
        print("📋 Ollama is optional but recommended for full functionality")
        print("🔗 Install from: https://ollama.ai")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "validation_reports",
        "bcm_rag_db",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def test_installation():
    """Test the installation"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        from bcm_expert import BCMExpert
        from bcm_validator import BCMValidator
        from validation_report import ValidationReportGenerator
        from iso_22301_standards import ISO22301Standards
        
        print("✅ All BCM Expert modules imported successfully")
        
        # Test standards database
        standards = ISO22301Standards()
        requirements = standards.get_all_requirements()
        print(f"✅ ISO 22301 standards loaded: {len(requirements)} requirements")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main installation function"""
    print("🏢 BCM Expert - Installation Script")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements. Please check your internet connection and try again.")
        sys.exit(1)
    
    # Install spaCy model
    if not install_spacy_model():
        print("⚠️  Failed to install spaCy model. Some NLP features may not work.")
    
    # Download NLTK data
    if not download_nltk_data():
        print("⚠️  Failed to download NLTK data. Some text processing features may not work.")
    
    # Check Ollama
    check_ollama()
    
    # Create directories
    create_directories()
    
    # Test installation
    if test_installation():
        print("\n🎉 Installation completed successfully!")
        print("\n🚀 Quick Start:")
        print("1. Place your BCM document (PDF) in the project directory")
        print("2. Run: python test.py")
        print("3. Choose option 1 for advanced validation or option 2 for basic validation")
        print("\n📚 For more information, see README.md")
    else:
        print("\n❌ Installation test failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()

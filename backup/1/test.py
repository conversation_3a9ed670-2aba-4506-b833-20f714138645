try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from pypdf import PdfReader
    except ImportError:
        print("Warning: Neither PyPDF2 nor pypdf found. PDF processing will not work.")
        PdfReader = None
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OllamaEmbeddings
from langchain.vectorstores import Chroma
from langchain.chains import RetrievalQA
from langchain.llms import Ollama
from langchain.output_parsers import StructuredOutputParser
from langchain.output_parsers.pydantic import PydanticOutputParser
from pydantic import BaseModel, Field
from typing import List
import os

# Import BCM validation modules
try:
    from bcm_expert import BCMExpert
    from bcm_validator import BCMValidator
    from validation_report import ValidationReportGenerator
    BCM_EXPERT_AVAILABLE = True
except ImportError:
    print("⚠️  BCM Expert modules not found. Running in basic mode.")
    BCM_EXPERT_AVAILABLE = False

# ---- Define ISO BCM Standard Validation Schema ----
class BCMValidationResult(BaseModel):
    standard_met: bool = Field(description="Whether the document meets the ISO BCM standards")
    missing_elements: List[str] = Field(description="List of required BCM elements missing from the document")
    recommendations: List[str] = Field(description="Recommendations to improve BCM compliance")
    evidence: List[str] = Field(description="Evidence from the document supporting the validation results")

# ---- New Function: Validate BCM Document Against ISO Standards ----
def validate_bcm_document(qa_chain):
    parser = PydanticOutputParser(pydantic_object=BCMValidationResult)

    validation_prompt = """
    You are a Business Continuity Management expert. Analyze the provided BCM document
    and validate if it follows ISO 22301 standards for BCM.

    Specifically check for:
    1. Context of the organization
    2. Leadership commitment
    3. Planning for business continuity
    4. Support resources and documentation
    5. Business impact analysis and risk assessment
    6. Business continuity strategies and solutions
    7. Plans and procedures for business continuity
    8. Performance evaluation
    9. Improvement processes

    {format_instructions}
    """

    full_prompt = validation_prompt.format(format_instructions=parser.get_format_instructions())
    response = qa_chain.run(full_prompt)

    try:
        return parser.parse(response)
    except Exception as e:
        print(f"Error parsing response: {e}")
        return {"error": "Failed to parse validation results", "raw_response": response}

# ---- Core Functions ----
def load_pdf_text(file_path):
    """Load text from PDF file"""
    reader = PdfReader(file_path)
    text = ""
    for page in reader.pages:
        text += page.extract_text() or ""
    return text

def load_llm_model():
    """Load LLM model from Ollama"""
    return Ollama(model="llama3.2")  # You can change this to your preferred model

def build_vector_db(text, persist_dir="bcm_rag_db"):
    """Build vector database from text"""
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=512, chunk_overlap=64)
    docs = text_splitter.create_documents([text])

    embeddings = OllamaEmbeddings(model="nomic-embed-text")
    vectorstore = Chroma.from_documents(docs, embedding=embeddings, persist_directory=persist_dir)
    return vectorstore

def build_qa_chain(llm, vectorstore):
    """Build QA chain"""
    return RetrievalQA.from_chain_type(
        llm=llm,
        chain_type="stuff",
        retriever=vectorstore.as_retriever()
    )

# ---- Advanced BCM Validation Functions ----
def validate_bcm_document_advanced(pdf_path):
    """Advanced BCM validation using BCM Expert modules"""
    if not BCM_EXPERT_AVAILABLE:
        print("❌ Advanced BCM validation not available. Install BCM Expert modules.")
        return None

    try:
        print("\n🔍 Starting comprehensive ISO 22301 compliance validation...")
        bcm_expert = BCMExpert()

        # Validate document
        result = bcm_expert.validate_document(pdf_path, output_dir='validation_reports')

        print(f"\n📊 Validation Results:")
        print(f"Overall Compliance Score: {result['summary']['overall_score']:.1f}%")
        print(f"Total Requirements Checked: {result['summary']['total_requirements']}")
        print(f"Critical Gaps Found: {result['summary']['critical_gaps']}")
        print(f"Recommendations: {result['summary']['recommendations']}")

        # Display compliance level
        score = result['summary']['overall_score']
        if score >= 80:
            print("\n✅ GOOD - Strong compliance with ISO 22301")
        elif score >= 60:
            print("\n⚠️  MODERATE - Reasonable compliance, improvements needed")
        elif score >= 40:
            print("\n❌ POOR - Significant gaps require attention")
        else:
            print("\n🚨 CRITICAL - Major compliance deficiencies")

        print(f"\n📁 Detailed reports saved to: validation_reports/")

        return result

    except Exception as e:
        print(f"❌ Advanced validation error: {e}")
        return None

def analyze_document_structure(pdf_path):
    """Analyze document structure"""
    if not BCM_EXPERT_AVAILABLE:
        print("❌ Document analysis not available. Install BCM Expert modules.")
        return None

    try:
        print("\n🔍 Analyzing document structure...")
        bcm_expert = BCMExpert()
        result = bcm_expert.analyze_document_structure(pdf_path)
        summary = result['summary']

        print(f"\n📋 Document Analysis:")
        print(f"  Title: {summary['title']}")
        print(f"  Pages: {summary['total_pages']}")
        print(f"  Word Count: {summary['word_count']}")
        print(f"  Sections: {summary['section_count']}")
        print(f"  Top Keywords: {', '.join(summary['top_keywords'][:10])}")

        return result

    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return None

def show_iso_standards_info():
    """Show ISO 22301 standards information"""
    if not BCM_EXPERT_AVAILABLE:
        print("❌ Standards info not available. Install BCM Expert modules.")
        return

    try:
        bcm_expert = BCMExpert()
        info = bcm_expert.get_iso_standards_info()

        print(f"\n📋 ISO 22301:2019 Standards:")
        print(f"  Total Requirements: {info['total_requirements']}")
        print(f"  Mandatory Documents: {len(info['mandatory_documents'])}")
        print(f"  Clause Groups: {len(info['clause_groups'])}")

        for clause, title in info['clause_groups'].items():
            print(f"    Clause {clause}: {title}")

    except Exception as e:
        print(f"❌ Standards info error: {e}")

# ---- Enhanced Main Function ----
def main():
    pdf_path = "BCM Plan - Ptech - 19082024.pdf"

    print("🏢 BCM Expert - ISO 22301 Document Validator")
    print("=" * 50)

    if not os.path.exists(pdf_path):
        print("❌ BCM PDF file not found.")
        print("Please ensure your BCM document is available.")
        return

    # Show available modes
    if BCM_EXPERT_AVAILABLE:
        print("\n🚀 Full BCM Expert mode available!")
        print("Available features:")
        print("  • Comprehensive ISO 22301 compliance validation")
        print("  • Document structure analysis")
        print("  • Interactive BCM Q&A with LLM")
        print("  • Detailed compliance reports")
    else:
        print("\n⚠️  Running in basic mode (BCM Expert modules not available)")
        print("Available features:")
        print("  • Basic ISO validation with LLM")
        print("  • Interactive BCM Q&A")

    print("\n🔍 Choose an option:")
    print("1. Advanced ISO 22301 compliance validation (if available)")
    print("2. Basic LLM-based validation")
    print("3. Analyze document structure (if available)")
    print("4. Show ISO 22301 standards info (if available)")
    print("5. Interactive BCM chat with LLM")
    print("6. Exit")

    choice = input("\nEnter your choice (1-6): ").strip()

    if choice == "1":
        # Advanced validation
        if BCM_EXPERT_AVAILABLE:
            validate_bcm_document_advanced(pdf_path)
        else:
            print("❌ Advanced validation not available. Try option 2 for basic validation.")

    elif choice == "2":
        # Basic LLM validation
        try:
            print("\n🔄 Loading BCM document...")
            document_text = load_pdf_text(pdf_path)

            print("🧠 Loading LLM model from Ollama...")
            llm = load_llm_model()

            print("📚 Building knowledge base with Nomic embeddings...")
            vectorstore = build_vector_db(document_text)

            print("🔗 Creating QA chain...")
            qa_chain = build_qa_chain(llm, vectorstore)

            print("\n🔍 Validating BCM document against ISO standards...")
            validation_result = validate_bcm_document(qa_chain)

            if isinstance(validation_result, dict) and "error" in validation_result:
                print(f"❌ Validation error: {validation_result['error']}")
            else:
                print("\n📋 Validation Results:")
                print(f"✅ Standards Met: {validation_result.standard_met}")
                print("\n❌ Missing Elements:")
                for item in validation_result.missing_elements:
                    print(f"  - {item}")
                print("\n💡 Recommendations:")
                for item in validation_result.recommendations:
                    print(f"  - {item}")
        except Exception as e:
            print(f"❌ Basic validation error: {e}")

    elif choice == "3":
        # Document analysis
        analyze_document_structure(pdf_path)

    elif choice == "4":
        # Standards info
        show_iso_standards_info()

    elif choice == "5":
        # Interactive chat
        try:
            print("\n🔄 Loading BCM document...")
            document_text = load_pdf_text(pdf_path)

            print("🧠 Loading LLM model from Ollama...")
            llm = load_llm_model()

            print("📚 Building knowledge base with Nomic embeddings...")
            vectorstore = build_vector_db(document_text)

            print("🔗 Creating QA chain...")
            qa_chain = build_qa_chain(llm, vectorstore)

            print("\n✅ BCM Expert Chat Ready!")
            print("Ask questions about your BCM document (type 'exit' to quit):")

            while True:
                query = input("\n🧾 BCM Question: ")
                if query.lower() in ["exit", "quit"]:
                    print("👋 Exiting BCM chat. Thank you!")
                    break

                try:
                    full_query = f"You are a Business Continuity expert with deep knowledge of ISO 22301 standards. Based on the BCM document provided, answer this question with specific references to compliance requirements where relevant: {query}"
                    response = qa_chain.run(full_query)
                    print("\n💬 BCM Expert Answer:", response)
                except Exception as e:
                    print(f"❌ Query error: {e}")
        except Exception as e:
            print(f"❌ Chat setup error: {e}")

    elif choice == "6":
        print("👋 Exiting BCM Expert. Thank you!")

    else:
        print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
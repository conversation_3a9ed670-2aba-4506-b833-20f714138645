#!/usr/bin/env python3
"""
Demo script for BCM Expert - ISO 22301 Document Validator
This script demonstrates the key features of the BCM validation system.
"""

import os
import sys
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

def print_section(title):
    """Print a formatted section header"""
    print(f"\n📋 {title}")
    print("-" * 40)

def demo_standards_info():
    """Demonstrate ISO 22301 standards information"""
    print_section("ISO 22301:2019 Standards Information")
    
    try:
        from iso_22301_standards import ISO22301Standards
        
        standards = ISO22301Standards()
        
        # Show basic info
        all_requirements = standards.get_all_requirements()
        mandatory_requirements = standards.get_mandatory_requirements()
        mandatory_docs = standards.get_mandatory_documents()
        
        print(f"📊 Total Requirements: {len(all_requirements)}")
        print(f"📊 Mandatory Requirements: {len(mandatory_requirements)}")
        print(f"📊 Mandatory Documents: {len(mandatory_docs)}")
        
        # Show clause groups
        print("\n📚 ISO 22301 Clause Groups:")
        clause_groups = {
            '4': 'Context of the Organization',
            '5': 'Leadership',
            '6': 'Planning',
            '7': 'Support',
            '8': 'Operation',
            '9': 'Performance Evaluation',
            '10': 'Improvement'
        }
        
        for clause, title in clause_groups.items():
            group_reqs = standards.get_requirements_by_clause_group(clause)
            print(f"  • Clause {clause}: {title} ({len(group_reqs)} requirements)")
        
        # Show sample mandatory documents
        print("\n📄 Sample Mandatory Documents:")
        for doc in mandatory_docs[:5]:
            print(f"  • {doc}")
        
        if len(mandatory_docs) > 5:
            print(f"  ... and {len(mandatory_docs) - 5} more")
        
        # Show sample requirement
        print("\n🔍 Sample Requirement (Clause 5.2):")
        req_5_2 = standards.get_requirement('5.2')
        if req_5_2:
            print(f"  Title: {req_5_2.title}")
            print(f"  Description: {req_5_2.description[:100]}...")
            print(f"  Keywords: {', '.join(req_5_2.validation_criteria.keywords[:5])}")
        
        return True
        
    except ImportError:
        print("❌ BCM Expert modules not available")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_document_analysis():
    """Demonstrate document analysis capabilities"""
    print_section("Document Analysis Demo")
    
    # Check for sample document
    sample_docs = [
        "BCM Plan - Ptech - 19082024.pdf",
        "sample_bcm_document.pdf",
        "bcm_plan.pdf"
    ]
    
    pdf_path = None
    for doc in sample_docs:
        if os.path.exists(doc):
            pdf_path = doc
            break
    
    if not pdf_path:
        print("⚠️  No sample BCM document found")
        print("📋 To test document analysis, place a PDF file with one of these names:")
        for doc in sample_docs:
            print(f"  • {doc}")
        return False
    
    try:
        from bcm_expert import BCMExpert
        
        print(f"📄 Analyzing document: {pdf_path}")
        bcm_expert = BCMExpert()
        
        # Analyze document structure
        result = bcm_expert.analyze_document_structure(pdf_path)
        summary = result['summary']
        
        print(f"\n📊 Document Summary:")
        print(f"  Title: {summary['title']}")
        print(f"  Pages: {summary['total_pages']}")
        print(f"  Word Count: {summary['word_count']:,}")
        print(f"  Sections: {summary['section_count']}")
        
        print(f"\n🔑 Top Keywords:")
        for i, keyword in enumerate(summary['top_keywords'][:10], 1):
            print(f"  {i:2d}. {keyword}")
        
        # Show compliance indicators
        indicators = result['compliance_indicators']
        print(f"\n📊 Compliance Indicators Found:")
        for indicator, items in indicators.items():
            if items:
                indicator_name = indicator.replace('_', ' ').title()
                print(f"  • {indicator_name}: {len(items)} references")
        
        return True
        
    except ImportError:
        print("❌ BCM Expert modules not available")
        return False
    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return False

def demo_validation():
    """Demonstrate BCM validation"""
    print_section("BCM Validation Demo")
    
    # Check for sample document
    pdf_path = "BCM Plan - Ptech - 19082024.pdf"
    if not os.path.exists(pdf_path):
        print("⚠️  Sample BCM document not found")
        print(f"📋 Place your BCM document as: {pdf_path}")
        return False
    
    try:
        from bcm_expert import BCMExpert
        
        print(f"🔍 Validating document: {pdf_path}")
        bcm_expert = BCMExpert()
        
        # Run validation
        result = bcm_expert.validate_document(pdf_path, output_dir='demo_reports')
        
        print(f"\n📊 Validation Results:")
        print(f"  Overall Compliance Score: {result['summary']['overall_score']:.1f}%")
        print(f"  Total Requirements: {result['summary']['total_requirements']}")
        print(f"  Critical Gaps: {result['summary']['critical_gaps']}")
        print(f"  Recommendations: {result['summary']['recommendations']}")
        
        # Show compliance level
        score = result['summary']['overall_score']
        if score >= 80:
            print("\n✅ GOOD - Strong compliance with ISO 22301")
        elif score >= 60:
            print("\n⚠️  MODERATE - Reasonable compliance, improvements needed")
        elif score >= 40:
            print("\n❌ POOR - Significant gaps require attention")
        else:
            print("\n🚨 CRITICAL - Major compliance deficiencies")
        
        # Show sample gaps and recommendations
        compliance_report = result['compliance_report']
        
        if compliance_report.critical_gaps:
            print(f"\n🚨 Critical Gaps (showing first 3):")
            for gap in compliance_report.critical_gaps[:3]:
                print(f"  • {gap}")
        
        if compliance_report.recommendations:
            print(f"\n💡 Recommendations (showing first 3):")
            for rec in compliance_report.recommendations[:3]:
                print(f"  • {rec}")
        
        print(f"\n📁 Detailed reports saved to: demo_reports/")
        
        return True
        
    except ImportError:
        print("❌ BCM Expert modules not available")
        return False
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def demo_report_generation():
    """Demonstrate report generation"""
    print_section("Report Generation Demo")
    
    try:
        from validation_report import ValidationReportGenerator
        from bcm_validator import ComplianceReport, ValidationResult, ComplianceLevel
        from datetime import datetime
        
        # Create a sample compliance report for demo
        sample_results = [
            ValidationResult(
                clause="5.2",
                title="Business continuity policy",
                compliance_level=ComplianceLevel.FULLY_COMPLIANT,
                score=95.0,
                findings=["Policy document found", "Management commitment evident"],
                evidence=["Section 2: BC Policy", "CEO signature on policy"],
                recommendations=[],
                gaps=[]
            ),
            ValidationResult(
                clause="8.2",
                title="Business impact analysis and risk assessment",
                compliance_level=ComplianceLevel.PARTIALLY_COMPLIANT,
                score=65.0,
                findings=["BIA conducted", "Risk assessment present"],
                evidence=["Section 4: BIA Results"],
                recommendations=["Update risk assessment methodology", "Include supply chain risks"],
                gaps=["Missing quantitative impact analysis", "Incomplete risk register"]
            )
        ]
        
        sample_report = ComplianceReport(
            document_title="Sample BCM Plan",
            validation_date=datetime.now(),
            overall_score=78.5,
            compliance_summary={
                "Fully Compliant": 15,
                "Partially Compliant": 8,
                "Non-Compliant": 2,
                "Not Applicable": 0
            },
            validation_results=sample_results,
            mandatory_documents_check={"Business continuity policy": True, "BIA results": False},
            recommendations=["Enhance risk assessment", "Update BIA methodology"],
            critical_gaps=["Missing disaster recovery procedures"],
            document_summary={"title": "Sample BCM Plan", "pages": 45, "sections": 12}
        )
        
        # Generate reports
        report_gen = ValidationReportGenerator()
        
        print("📊 Generating sample reports...")
        
        # Executive summary
        exec_summary = report_gen.generate_executive_summary(sample_report)
        print("\n📋 Executive Summary (first 300 chars):")
        print(exec_summary[:300] + "...")
        
        # Gap analysis
        gap_analysis = report_gen.generate_gap_analysis(sample_report)
        print("\n🔍 Gap Analysis (first 200 chars):")
        print(gap_analysis[:200] + "...")
        
        # Action plan
        action_plan = report_gen.generate_action_plan(sample_report)
        print("\n📋 Action Plan (first 200 chars):")
        print(action_plan[:200] + "...")
        
        print("\n✅ Report generation demo completed")
        print("📁 In actual usage, reports are saved as HTML, Excel, and CSV files")
        
        return True
        
    except ImportError:
        print("❌ BCM Expert modules not available")
        return False
    except Exception as e:
        print(f"❌ Report generation error: {e}")
        return False

def main():
    """Main demo function"""
    print_header("BCM Expert - ISO 22301 Document Validator Demo")
    
    print("🎯 This demo showcases the key features of BCM Expert:")
    print("  • ISO 22301 standards database")
    print("  • Document analysis capabilities")
    print("  • Compliance validation")
    print("  • Report generation")
    
    # Check if BCM Expert modules are available
    try:
        import bcm_expert
        print("\n✅ BCM Expert modules are available")
    except ImportError:
        print("\n⚠️  BCM Expert modules not found")
        print("📋 To install, run: python install.py")
        print("📋 Or manually install requirements: pip install -r requirements.txt")
    
    # Run demos
    demos = [
        ("ISO 22301 Standards Information", demo_standards_info),
        ("Document Analysis", demo_document_analysis),
        ("BCM Validation", demo_validation),
        ("Report Generation", demo_report_generation)
    ]
    
    for demo_name, demo_func in demos:
        print_header(f"Demo: {demo_name}")
        
        try:
            success = demo_func()
            if success:
                print(f"\n✅ {demo_name} demo completed successfully")
            else:
                print(f"\n⚠️  {demo_name} demo completed with warnings")
        except Exception as e:
            print(f"\n❌ {demo_name} demo failed: {e}")
        
        input("\nPress Enter to continue to next demo...")
    
    print_header("Demo Complete")
    print("🎉 BCM Expert demo completed!")
    print("\n🚀 Next Steps:")
    print("1. Place your BCM document in the project directory")
    print("2. Run: python test.py")
    print("3. Choose validation options to analyze your document")
    print("4. Review generated reports for compliance insights")
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()

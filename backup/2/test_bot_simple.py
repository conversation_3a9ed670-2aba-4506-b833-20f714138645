#!/usr/bin/env python3
"""
Simple test for Bot BCM Validator without requiring llama-cpp-python
"""

import os
import sys

def test_model_path():
    """Test if the model path is accessible"""
    
    print("🧪 Testing Model Path Access")
    print("=" * 40)
    
    model_path = "E:/susan_chatbot_test/model/mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    
    if os.path.exists(model_path):
        print(f"✅ Model found: {model_path}")
        
        # Get file size
        size_mb = os.path.getsize(model_path) / (1024 * 1024)
        print(f"✅ Model size: {size_mb:.1f} MB")
        
        return True
    else:
        print(f"❌ Model not found: {model_path}")
        return False

def test_document_path():
    """Test if the BCM document is accessible"""
    
    print("\n🧪 Testing Document Path Access")
    print("=" * 40)
    
    document_paths = [
        "BCM Plan - Ptech - 19082024.pdf",
        "E:/susan_chatbot_test/BCM Plan - Ptech - 19082024.pdf"
    ]
    
    for doc_path in document_paths:
        if os.path.exists(doc_path):
            print(f"✅ Document found: {doc_path}")
            
            # Get file size
            size_mb = os.path.getsize(doc_path) / (1024 * 1024)
            print(f"✅ Document size: {size_mb:.1f} MB")
            
            return doc_path
    
    print("❌ BCM document not found in any location")
    return None

def test_bot_validator_import():
    """Test if bot validator can be imported"""
    
    print("\n🧪 Testing Bot Validator Import")
    print("=" * 40)
    
    try:
        # Add bot_validation to path
        sys.path.append('bot_validation')
        
        # Test document processor
        from bot_document_processor import BotDocumentProcessor
        processor = BotDocumentProcessor()
        print("✅ Document processor imported successfully")
        
        # Test model interface (without loading)
        from mistral_model import MistralBCMModel
        model = MistralBCMModel("dummy_path")
        print("✅ Model interface imported successfully")
        
        # Test main validator
        from bot_bcm_validator import BotBCMValidator
        validator = BotBCMValidator("dummy_path")
        print("✅ Bot validator imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_document_processing():
    """Test document processing without AI"""
    
    print("\n🧪 Testing Document Processing")
    print("=" * 40)
    
    doc_path = test_document_path()
    if not doc_path:
        print("⚠️  Skipping document processing test - no document found")
        return False
    
    try:
        sys.path.append('bot_validation')
        from bot_document_processor import BotDocumentProcessor
        
        processor = BotDocumentProcessor()
        print("🔄 Processing document...")
        
        document = processor.process_document(doc_path)
        
        print(f"✅ Document processed successfully:")
        print(f"   Title: {document.title}")
        print(f"   Pages: {document.total_pages}")
        print(f"   Words: {document.total_words:,}")
        print(f"   Sections: {len(document.sections)}")
        
        # Test section classification
        critical_sections = processor.get_critical_sections(document)
        print(f"✅ Critical sections found: {list(critical_sections.keys())}")
        
        # Test contact extraction
        contacts = processor.extract_contact_information(document)
        print(f"✅ Contacts extracted: {len(contacts)} found")
        
        # Test vendor extraction
        vendors = processor.extract_vendor_information(document)
        print(f"✅ Vendors extracted: {len(vendors)} found")
        
        return True
        
    except Exception as e:
        print(f"❌ Document processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_next_steps():
    """Show next steps for full AI validation"""
    
    print("\n🎯 NEXT STEPS FOR FULL AI VALIDATION")
    print("=" * 50)
    
    print("To enable full AI-powered validation:")
    print()
    print("1. Install llama-cpp-python:")
    print("   pip install llama-cpp-python")
    print()
    print("2. Run the bot validator:")
    print("   python bot_validation/main.py")
    print()
    print("3. Or specify document directly:")
    print("   python bot_validation/main.py \"BCM Plan - Ptech - 19082024.pdf\"")
    print()
    print("Expected AI features:")
    print("• Intelligent section analysis")
    print("• Contextual compliance assessment")
    print("• Specific improvement recommendations")
    print("• Executive summaries")
    print("• Detailed AI insights")

def main():
    """Run all tests"""
    
    print("🤖 Bot BCM Validator - Simple Test Suite")
    print("=" * 50)
    
    tests = [
        ("Model Path Access", test_model_path),
        ("Bot Validator Import", test_bot_validator_import),
        ("Document Processing", test_document_processing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - CRASHED: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed >= 2:  # Model path and imports working
        print("🎉 Bot BCM Validator is ready for AI-powered analysis!")
        show_next_steps()
    else:
        print("⚠️  Some components need attention before running AI validation.")

if __name__ == "__main__":
    main()

"""
Main application for Bot BCM Validator
AI-powered BCM document validation using Mistral 7B model.
"""

import os
import sys
from datetime import datetime

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from .bot_bcm_validator import BotBCMValidator
except ImportError:
    from bot_bcm_validator import BotBCMValidator

def check_model_availability():
    """Check if Mistral 7B model is available"""

    model_paths = [
        "E:/susan_chatbot_test/model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        "../model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        "mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    ]

    for path in model_paths:
        if os.path.exists(path):
            return path

    return None

def main():
    """Main application function"""

    print("🤖 Bot BCM Validator - AI-Powered ISO 22301 Analysis")
    print("=" * 60)
    print("Uses Mistral 7B AI model for intelligent BCM document validation")
    print()

    # Check model availability
    model_path = check_model_availability()
    if not model_path:
        print("❌ Mistral 7B model not found!")
        print()
        print("Please ensure the model file is available at one of these locations:")
        print("  • model/mistral-7b-instruct-v0.1.Q4_K_M.gguf")
        print("  • ../model/mistral-7b-instruct-v0.1.Q4_K_M.gguf")
        print("  • mistral-7b-instruct-v0.1.Q4_K_M.gguf")
        print()
        print("Download the model from: https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF")
        return

    print(f"✅ Found Mistral 7B model: {model_path}")

    # Get document path
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        # Look for common BCM document names
        common_names = [
            "BCM Plan - Ptech - 19082024.pdf",
            "../BCM Plan - Ptech - 19082024.pdf",
            "E:/susan_chatbot_test/BCM Plan - Ptech - 19082024.pdf",
            "bcm_plan.pdf",
            "business_continuity_plan.pdf",
            "bc_plan.pdf"
        ]

        pdf_path = None
        for name in common_names:
            if os.path.exists(name):
                pdf_path = name
                break

        if not pdf_path:
            print("\n📄 Please specify your BCM document:")
            print("   python main.py your_bcm_document.pdf")
            print()
            print("Or place your document in this folder with one of these names:")
            for name in common_names:
                if not name.startswith("../"):
                    print(f"   • {name}")
            return

    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return

    print(f"\n📄 Validating document: {pdf_path}")
    print("⚠️  Note: AI analysis may take several minutes depending on document size")
    print()

    try:
        # Initialize validator with model path
        validator = BotBCMValidator(model_path)

        # Validate document
        print("🚀 Starting AI-powered validation...")
        report = validator.validate_document(pdf_path)

        # Create output directory
        output_dir = "bot_validation_output"
        os.makedirs(output_dir, exist_ok=True)

        # Generate timestamp for filenames
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Generate detailed report
        detailed_report_file = f"{output_dir}/ai_bcm_validation_report_{timestamp}.txt"
        detailed_report = validator.generate_detailed_report(report, detailed_report_file)

        # Generate executive summary
        executive_summary_file = f"{output_dir}/ai_executive_summary_{timestamp}.txt"
        executive_summary = validator.generate_executive_summary(report, executive_summary_file)

        # Display summary
        print("\n" + "=" * 60)
        print("🤖 AI VALIDATION SUMMARY")
        print("=" * 60)
        print(f"Document: {report.document_title}")
        print(f"Assessment: {report.overall_assessment}")
        print(f"AI Model: {report.model_used}")
        print(f"Processing Time: {report.total_processing_time:.1f} seconds")
        print()

        # Show compliance breakdown
        total_analyzed = sum(report.compliance_summary.values())
        if total_analyzed > 0:
            print("📊 COMPLIANCE BREAKDOWN:")
            for status, count in report.compliance_summary.items():
                if count > 0:
                    percentage = (count / total_analyzed * 100)
                    print(f"   • {status}: {count} sections ({percentage:.1f}%)")
            print()

        # Show document statistics
        print("📋 DOCUMENT ANALYSIS:")
        print(f"   • Pages: {report.document_stats['total_pages']}")
        print(f"   • Words: {report.document_stats['total_words']:,}")
        print(f"   • Sections: {report.document_stats['total_sections']}")
        print(f"   • Contacts Found: {report.document_stats['contact_count']}")
        print(f"   • Vendors Found: {report.document_stats['vendor_count']}")
        print()

        # Show key AI insights
        if report.findings:
            non_compliant = [f for f in report.findings if f.compliance_status == "Non-Compliant"]
            if non_compliant:
                print("🚨 KEY AI FINDINGS (Non-Compliant Sections):")
                for finding in non_compliant[:3]:
                    print(f"   • {finding.section_title}: {finding.specific_issues[0] if finding.specific_issues else 'Issues identified'}")
                if len(non_compliant) > 3:
                    print(f"   ... and {len(non_compliant) - 3} more non-compliant sections")
                print()

        # Show generated files
        print("📁 GENERATED AI REPORTS:")
        print(f"   • Detailed Analysis: {detailed_report_file}")
        print(f"   • Executive Summary: {executive_summary_file}")
        print()

        # Show next steps
        print("🎯 NEXT STEPS:")
        print("   1. Review the detailed AI analysis report")
        print("   2. Share the executive summary with management")
        print("   3. Address non-compliant sections identified by AI")
        print("   4. Implement AI-recommended fixes")
        print("   5. Re-run AI validation after improvements")
        print()

        print("✅ AI validation complete! Check the generated reports for detailed insights.")

    except Exception as e:
        print(f"❌ Error during AI validation: {e}")
        import traceback
        traceback.print_exc()

def interactive_mode():
    """Interactive mode for AI validation"""

    print("🤖 Bot BCM Validator - Interactive AI Mode")
    print("=" * 50)

    while True:
        print("\nAI Validation Options:")
        print("1. Run full AI validation")
        print("2. Check model availability")
        print("3. Explain ISO 22301 requirement")
        print("4. Exit")

        choice = input("\nSelect option (1-4): ").strip()

        if choice == "1":
            pdf_path = input("Enter path to your BCM document (PDF): ").strip()
            if pdf_path and os.path.exists(pdf_path):
                # Temporarily change sys.argv for main function
                original_argv = sys.argv[:]
                sys.argv = [sys.argv[0], pdf_path]
                main()
                sys.argv = original_argv
            else:
                print("❌ File not found or invalid path")

        elif choice == "2":
            model_path = check_model_availability()
            if model_path:
                print(f"✅ Mistral 7B model found: {model_path}")

                # Test model loading
                try:
                    from mistral_model import MistralBCMModel
                    model = MistralBCMModel(model_path)
                    if model.load_model():
                        print("✅ Model loads successfully")
                        model.unload_model()
                    else:
                        print("❌ Model failed to load")
                except Exception as e:
                    print(f"❌ Model test failed: {e}")
            else:
                print("❌ Mistral 7B model not found")

        elif choice == "3":
            explain_iso_requirement()

        elif choice == "4":
            print("👋 Thank you for using Bot BCM Validator!")
            break

        else:
            print("❌ Invalid option. Please try again.")

def explain_iso_requirement():
    """Explain ISO 22301 requirements using AI"""

    print("\nISO 22301 Requirements:")
    print("4.3 - Scope determination")
    print("5.2 - Business continuity policy")
    print("6.2 - Business continuity objectives")
    print("7.2 - Competence and training")
    print("8.2 - Business impact analysis and risk assessment")
    print("8.4 - Business continuity plans and procedures")
    print("8.5 - Exercising and testing")
    print("9.2 - Internal audit")

    clause = input("\nEnter clause number (e.g., 5.2): ").strip()

    # Simple explanations (could be enhanced with AI if model is available)
    explanations = {
        "4.3": "Scope - Define what your BC plan covers and what it excludes",
        "5.2": "Policy - Management commitment statement for business continuity",
        "6.2": "Objectives - Specific, measurable BC goals with timeframes",
        "7.2": "Training - Staff competence and training for BC roles",
        "8.2": "BIA & Risk - Analyze business impacts and assess risks",
        "8.4": "Plans - Detailed procedures for response and recovery",
        "8.5": "Testing - Regular exercises to test your BC plans",
        "9.2": "Audit - Internal reviews to check BC system effectiveness"
    }

    if clause in explanations:
        print(f"\n📋 ISO 22301 Clause {clause}:")
        print(explanations[clause])
    else:
        print("❌ Clause not found. Please use format like '5.2'")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        main()

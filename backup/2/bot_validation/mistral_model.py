"""
Mistral 7B Model Interface for BCM Validation
Handles loading and interaction with the Mistral 7B model for BCM document analysis.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    print("Warning: llama-cpp-python not available. Install with: pip install llama-cpp-python")

@dataclass
class ModelResponse:
    """Response from the Mistral model"""
    content: str
    confidence: float
    processing_time: float

class MistralBCMModel:
    """Mistral 7B model interface for BCM validation"""
    
    def __init__(self, model_path: str = "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf"):
        self.model_path = model_path
        self.model = None
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging for the model"""
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def load_model(self) -> bool:
        """Load the Mistral 7B model"""
        
        if not LLAMA_CPP_AVAILABLE:
            self.logger.error("llama-cpp-python not available. Cannot load model.")
            return False
        
        if not os.path.exists(self.model_path):
            self.logger.error(f"Model file not found: {self.model_path}")
            return False
        
        try:
            self.logger.info(f"Loading Mistral 7B model from: {self.model_path}")
            self.model = Llama(
                model_path=self.model_path,
                n_ctx=4096,  # Context window
                n_threads=4,  # Number of threads
                verbose=False
            )
            self.logger.info("Mistral 7B model loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            return False
    
    def is_loaded(self) -> bool:
        """Check if model is loaded"""
        return self.model is not None
    
    def generate_response(self, prompt: str, max_tokens: int = 1000, temperature: float = 0.3) -> ModelResponse:
        """Generate response from the model"""
        
        if not self.is_loaded():
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        try:
            import time
            start_time = time.time()
            
            # Generate response
            response = self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                repeat_penalty=1.1,
                stop=["</s>", "Human:", "Assistant:"]
            )
            
            processing_time = time.time() - start_time
            
            # Extract content
            content = response['choices'][0]['text'].strip()
            
            # Simple confidence estimation based on response length and coherence
            confidence = min(1.0, len(content) / (max_tokens * 0.5))
            
            return ModelResponse(
                content=content,
                confidence=confidence,
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return ModelResponse(
                content=f"Error: {e}",
                confidence=0.0,
                processing_time=0.0
            )
    
    def analyze_bcm_section(self, section_title: str, section_content: str, iso_requirement: str) -> ModelResponse:
        """Analyze a specific BCM section against ISO requirements"""
        
        prompt = f"""You are a Business Continuity Management expert specializing in ISO 22301:2019 standards. 

Analyze the following BCM document section and provide specific, actionable feedback:

ISO 22301 Requirement: {iso_requirement}

Document Section: {section_title}
Content: {section_content[:2000]}

Please provide:
1. COMPLIANCE STATUS: (Compliant/Partially Compliant/Non-Compliant)
2. SPECIFIC ISSUES: What exactly is missing or unclear?
3. PRACTICAL FIXES: Specific actions to improve compliance
4. EXAMPLES: Show exactly what should be added or changed

Focus on practical, employee-friendly recommendations that can be easily implemented.

Response:"""

        return self.generate_response(prompt, max_tokens=800, temperature=0.2)
    
    def validate_contact_information(self, content: str) -> ModelResponse:
        """Validate contact information in the document"""
        
        prompt = f"""You are a BCM expert reviewing contact information for emergency response effectiveness.

Analyze this content for contact information quality:

Content: {content[:1500]}

Check for:
1. Are phone numbers provided for key personnel?
2. Are email addresses included?
3. Are backup contacts specified?
4. Are contact details current and complete?
5. Are roles clearly assigned to specific people?

Provide specific feedback on what's missing and how to fix it. Give examples of good vs. poor contact information.

Response:"""

        return self.generate_response(prompt, max_tokens=600, temperature=0.2)
    
    def analyze_procedures_clarity(self, content: str) -> ModelResponse:
        """Analyze procedures for clarity and specificity"""
        
        prompt = f"""You are a BCM expert evaluating emergency procedures for clarity and actionability.

Analyze these procedures:

Content: {content[:1500]}

Evaluate:
1. Are procedures step-by-step and specific?
2. Are there vague terms like "contact relevant parties" or "as soon as possible"?
3. Are roles and responsibilities clearly defined?
4. Would someone unfamiliar with the organization understand what to do?

Provide specific examples of vague language found and suggest exact replacements.

Response:"""

        return self.generate_response(prompt, max_tokens=600, temperature=0.2)
    
    def check_recovery_objectives(self, content: str) -> ModelResponse:
        """Check for recovery time and point objectives"""
        
        prompt = f"""You are a BCM expert reviewing recovery objectives and timeframes.

Analyze this content for recovery objectives:

Content: {content[:1500]}

Check for:
1. Are Recovery Time Objectives (RTO) specified with exact timeframes?
2. Are Recovery Point Objectives (RPO) defined?
3. Are impact timeframes specified (1 hour, 4 hours, 1 day, etc.)?
4. Are recovery priorities clearly defined?

Identify missing timeframes and suggest specific RTO/RPO values for different business functions.

Response:"""

        return self.generate_response(prompt, max_tokens=600, temperature=0.2)
    
    def evaluate_vendor_information(self, content: str) -> ModelResponse:
        """Evaluate vendor and supplier information completeness"""
        
        prompt = f"""You are a BCM expert reviewing vendor and supplier information for business continuity.

Analyze this vendor information:

Content: {content[:1500]}

Check for:
1. Are vendor contact details complete (names, phones, emails)?
2. Are backup/alternative vendors identified?
3. Are service level agreements mentioned?
4. Are vendor dependencies clearly mapped?
5. Are vendor business continuity capabilities assessed?

Provide specific recommendations for improving vendor information management.

Response:"""

        return self.generate_response(prompt, max_tokens=600, temperature=0.2)
    
    def assess_training_competence(self, content: str) -> ModelResponse:
        """Assess training and competence documentation"""
        
        prompt = f"""You are a BCM expert reviewing training and competence requirements per ISO 22301.

Analyze this training content:

Content: {content[:1500]}

Evaluate:
1. Are training records documented with dates and participants?
2. Are competency requirements defined for BC roles?
3. Are training materials and methods specified?
4. Is training effectiveness measured?
5. Are refresher training schedules established?

Provide specific recommendations for improving training documentation and competence management.

Response:"""

        return self.generate_response(prompt, max_tokens=600, temperature=0.2)
    
    def generate_improvement_summary(self, document_title: str, findings: List[str]) -> ModelResponse:
        """Generate an overall improvement summary"""
        
        findings_text = "\n".join([f"- {finding}" for finding in findings[:10]])
        
        prompt = f"""You are a senior BCM consultant providing executive summary and recommendations.

Document: {document_title}

Key Findings:
{findings_text}

Provide:
1. EXECUTIVE SUMMARY: Overall document assessment (2-3 sentences)
2. TOP 3 PRIORITIES: Most critical improvements needed
3. QUICK WINS: Easy fixes that can be done immediately
4. RESOURCE REQUIREMENTS: What resources/expertise needed
5. TIMELINE: Suggested implementation timeline

Keep recommendations practical and business-focused.

Response:"""

        return self.generate_response(prompt, max_tokens=800, temperature=0.3)
    
    def explain_iso_requirement(self, clause: str, requirement_title: str) -> ModelResponse:
        """Explain an ISO 22301 requirement in simple terms"""
        
        prompt = f"""You are a BCM trainer explaining ISO 22301 requirements to employees in simple, practical terms.

ISO 22301 Clause {clause}: {requirement_title}

Explain:
1. What this requirement means in plain English
2. Why it's important for business continuity
3. What employees need to do to meet this requirement
4. Common mistakes to avoid
5. Simple examples of good implementation

Use everyday language that any employee can understand.

Response:"""

        return self.generate_response(prompt, max_tokens=600, temperature=0.3)
    
    def unload_model(self):
        """Unload the model to free memory"""
        if self.model:
            del self.model
            self.model = None
            self.logger.info("Model unloaded")

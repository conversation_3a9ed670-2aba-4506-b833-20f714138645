"""
Document Processor for Bot Validation
Processes BCM documents and prepares content for AI analysis.
"""

import re
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass

try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from pypdf import PdfReader
    except ImportError:
        print("Warning: Neither PyPDF2 nor pypdf found. PDF processing will not work.")
        PdfReader = None

@dataclass
class DocumentSection:
    """Document section for AI analysis"""
    title: str
    content: str
    page_number: int
    section_type: str  # 'policy', 'procedure', 'contact', 'vendor', etc.
    word_count: int

@dataclass
class ProcessedDocument:
    """Processed document ready for AI analysis"""
    title: str
    total_pages: int
    total_words: int
    sections: List[DocumentSection]
    full_text: str
    metadata: Dict[str, Any]

class BotDocumentProcessor:
    """Document processor optimized for AI analysis"""
    
    def __init__(self):
        self.section_classifiers = self._initialize_section_classifiers()
    
    def _initialize_section_classifiers(self) -> Dict[str, List[str]]:
        """Initialize patterns to classify section types"""
        
        return {
            'policy': [
                r'policy', r'commitment', r'statement', r'governance',
                r'management\s+commitment', r'organizational\s+policy'
            ],
            'scope': [
                r'scope', r'boundary', r'boundaries', r'inclusion', r'exclusion',
                r'applicability', r'coverage'
            ],
            'objectives': [
                r'objective', r'goal', r'target', r'aim', r'purpose',
                r'business\s+continuity\s+objective'
            ],
            'roles': [
                r'role', r'responsibility', r'accountable', r'responsible',
                r'organization', r'structure', r'authority', r'raci'
            ],
            'contact': [
                r'contact', r'emergency\s+contact', r'phone', r'email',
                r'notification', r'communication\s+list', r'directory'
            ],
            'vendor': [
                r'vendor', r'supplier', r'contractor', r'third\s+party',
                r'service\s+provider', r'outsourced'
            ],
            'procedure': [
                r'procedure', r'process', r'instruction', r'step',
                r'workflow', r'method', r'protocol'
            ],
            'bia': [
                r'business\s+impact\s+analysis', r'bia', r'impact\s+assessment',
                r'criticality', r'dependency', r'impact\s+analysis'
            ],
            'risk': [
                r'risk\s+assessment', r'risk\s+analysis', r'threat',
                r'vulnerability', r'risk\s+management', r'hazard'
            ],
            'recovery': [
                r'recovery', r'restoration', r'resumption', r'continuity\s+plan',
                r'rto', r'rpo', r'recovery\s+time', r'recovery\s+point'
            ],
            'testing': [
                r'test', r'exercise', r'drill', r'simulation',
                r'validation', r'rehearsal', r'practice'
            ],
            'training': [
                r'training', r'awareness', r'education', r'competence',
                r'skill', r'knowledge', r'learning'
            ],
            'audit': [
                r'audit', r'review', r'assessment', r'evaluation',
                r'inspection', r'verification', r'compliance\s+check'
            ],
            'improvement': [
                r'improvement', r'enhancement', r'optimization',
                r'corrective\s+action', r'preventive\s+action', r'lesson\s+learned'
            ]
        }
    
    def process_document(self, file_path: str) -> ProcessedDocument:
        """Process BCM document for AI analysis"""
        
        if PdfReader is None:
            raise ImportError("PDF reader not available. Please install PyPDF2 or pypdf.")
        
        try:
            # Extract text and metadata
            reader = PdfReader(file_path)
            full_text = ""
            
            # Extract text from all pages
            for page_num, page in enumerate(reader.pages, 1):
                page_text = page.extract_text() or ""
                full_text += f"\n--- PAGE {page_num} ---\n{page_text}"
            
            # Get metadata
            metadata = {}
            if reader.metadata:
                metadata = {
                    'title': reader.metadata.get('/Title', ''),
                    'author': reader.metadata.get('/Author', ''),
                    'subject': reader.metadata.get('/Subject', ''),
                    'creation_date': reader.metadata.get('/CreationDate', ''),
                }
            
            # Extract title
            title = metadata.get('title', '')
            if not title:
                # Try to extract from first few lines
                first_lines = full_text.split('\n')[:10]
                for line in first_lines:
                    line = line.strip()
                    if len(line) > 10 and len(line) < 100 and not line.startswith('---'):
                        title = line
                        break
            
            # Process sections
            sections = self._extract_and_classify_sections(full_text)
            
            # Calculate statistics
            total_pages = len(reader.pages)
            total_words = len(full_text.split())
            
            return ProcessedDocument(
                title=title or "BCM Document",
                total_pages=total_pages,
                total_words=total_words,
                sections=sections,
                full_text=full_text,
                metadata=metadata
            )
            
        except Exception as e:
            raise Exception(f"Error processing document: {e}")
    
    def _extract_and_classify_sections(self, text: str) -> List[DocumentSection]:
        """Extract sections and classify them by type"""
        
        sections = []
        lines = text.split('\n')
        current_section = None
        current_content = []
        page_number = 1
        
        # Patterns for identifying headings
        heading_patterns = [
            r'^(\d+\.?\d*\.?\d*)\s+([A-Z][^a-z]*[A-Z].*)',  # Numbered headings
            r'^([A-Z][A-Z\s]+)$',  # ALL CAPS headings
            r'^([A-Z][a-zA-Z\s]+):?\s*$',  # Title case headings
            r'^(CHAPTER|SECTION|PART|APPENDIX)\s+(\d+)',  # Chapter/Section headings
        ]
        
        for line in lines:
            line = line.strip()
            
            # Track page numbers
            if line.startswith('--- PAGE'):
                page_match = re.search(r'PAGE (\d+)', line)
                if page_match:
                    page_number = int(page_match.group(1))
                continue
            
            # Skip empty lines
            if not line:
                continue
            
            # Check if line is a heading
            is_heading = False
            heading_title = line
            
            for pattern in heading_patterns:
                match = re.match(pattern, line)
                if match:
                    is_heading = True
                    if len(match.groups()) > 1:
                        heading_title = match.group(2).strip()
                    else:
                        heading_title = match.group(1).strip()
                    break
            
            # Also check for BCM-specific section patterns
            if not is_heading:
                bcm_patterns = [
                    r'^(?:business\s+continuity|bc)\s+(?:policy|plan|procedure)',
                    r'^(?:risk\s+assessment|business\s+impact\s+analysis|bia)',
                    r'^(?:emergency\s+response|incident\s+management)',
                    r'^(?:recovery\s+procedures|restoration\s+plan)',
                    r'^(?:communication\s+plan|notification\s+procedure)',
                    r'^(?:roles\s+and\s+responsibilities|organization)',
                    r'^(?:contact\s+information|emergency\s+contacts)',
                    r'^(?:vendor|supplier)\s+(?:information|management)',
                    r'^(?:testing|exercise)\s+(?:plan|procedure)',
                    r'^(?:training|awareness)\s+(?:plan|program)'
                ]
                
                for pattern in bcm_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        is_heading = True
                        heading_title = line
                        break
            
            if is_heading:
                # Save previous section
                if current_section and current_content:
                    content = '\n'.join(current_content)
                    current_section.content = content
                    current_section.word_count = len(content.split())
                    current_section.section_type = self._classify_section_type(current_section.title, content)
                    sections.append(current_section)
                
                # Start new section
                current_section = DocumentSection(
                    title=heading_title,
                    content="",
                    page_number=page_number,
                    section_type="unknown",
                    word_count=0
                )
                current_content = []
            else:
                # Add to current section content
                if current_section:
                    current_content.append(line)
                else:
                    # Create a default section for content before first heading
                    current_section = DocumentSection(
                        title="Introduction",
                        content="",
                        page_number=page_number,
                        section_type="introduction",
                        word_count=0
                    )
                    current_content = [line]
        
        # Add final section
        if current_section and current_content:
            content = '\n'.join(current_content)
            current_section.content = content
            current_section.word_count = len(content.split())
            current_section.section_type = self._classify_section_type(current_section.title, content)
            sections.append(current_section)
        
        return sections
    
    def _classify_section_type(self, title: str, content: str) -> str:
        """Classify section type based on title and content"""
        
        combined_text = (title + " " + content).lower()
        
        # Score each section type
        type_scores = {}
        for section_type, patterns in self.section_classifiers.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, combined_text, re.IGNORECASE))
                score += matches
            
            if score > 0:
                type_scores[section_type] = score
        
        # Return the highest scoring type
        if type_scores:
            return max(type_scores, key=type_scores.get)
        else:
            return "general"
    
    def get_sections_by_type(self, document: ProcessedDocument, section_type: str) -> List[DocumentSection]:
        """Get all sections of a specific type"""
        return [section for section in document.sections if section.section_type == section_type]
    
    def get_critical_sections(self, document: ProcessedDocument) -> Dict[str, List[DocumentSection]]:
        """Get sections critical for ISO 22301 compliance"""
        
        critical_types = [
            'policy', 'scope', 'objectives', 'roles', 'contact', 
            'vendor', 'bia', 'risk', 'recovery', 'testing', 'training', 'audit'
        ]
        
        critical_sections = {}
        for section_type in critical_types:
            sections = self.get_sections_by_type(document, section_type)
            if sections:
                critical_sections[section_type] = sections
        
        return critical_sections
    
    def prepare_content_for_analysis(self, section: DocumentSection, max_length: int = 2000) -> str:
        """Prepare section content for AI analysis"""
        
        content = section.content
        
        # Clean up the content
        content = re.sub(r'\s+', ' ', content)  # Normalize whitespace
        content = re.sub(r'[^\w\s\.,;:!?()-]', '', content)  # Remove special characters
        
        # Truncate if too long
        if len(content) > max_length:
            content = content[:max_length] + "..."
        
        return content.strip()
    
    def extract_contact_information(self, document: ProcessedDocument) -> List[Dict[str, str]]:
        """Extract contact information from the document"""
        
        contacts = []
        
        # Patterns for contact information
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        name_pattern = r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b'
        
        for section in document.sections:
            if section.section_type in ['contact', 'roles', 'procedure']:
                # Find names
                names = re.findall(name_pattern, section.content)
                phones = re.findall(phone_pattern, section.content)
                emails = re.findall(email_pattern, section.content)
                
                # Try to associate names with contact details
                for name in names:
                    contact = {'name': name, 'section': section.title}
                    
                    # Look for phone/email near the name
                    name_pos = section.content.find(name)
                    if name_pos != -1:
                        context = section.content[max(0, name_pos-100):name_pos+200]
                        
                        phone_match = re.search(phone_pattern, context)
                        if phone_match:
                            contact['phone'] = phone_match.group()
                        
                        email_match = re.search(email_pattern, context)
                        if email_match:
                            contact['email'] = email_match.group()
                    
                    contacts.append(contact)
        
        return contacts
    
    def extract_vendor_information(self, document: ProcessedDocument) -> List[Dict[str, str]]:
        """Extract vendor/supplier information"""
        
        vendors = []
        vendor_sections = self.get_sections_by_type(document, 'vendor')
        
        for section in vendor_sections:
            # Look for vendor names and details
            lines = section.content.split('\n')
            
            for line in lines:
                if any(keyword in line.lower() for keyword in ['vendor', 'supplier', 'contractor']):
                    vendor_info = {'section': section.title, 'description': line.strip()}
                    
                    # Look for contact details in the same line or nearby
                    phone_match = re.search(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', line)
                    if phone_match:
                        vendor_info['phone'] = phone_match.group()
                    
                    email_match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', line)
                    if email_match:
                        vendor_info['email'] = email_match.group()
                    
                    vendors.append(vendor_info)
        
        return vendors
    
    def get_document_statistics(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Get document statistics for analysis"""
        
        # Count sections by type
        section_counts = {}
        for section in document.sections:
            section_counts[section.section_type] = section_counts.get(section.section_type, 0) + 1
        
        # Extract contact and vendor counts
        contacts = self.extract_contact_information(document)
        vendors = self.extract_vendor_information(document)
        
        return {
            'total_pages': document.total_pages,
            'total_words': document.total_words,
            'total_sections': len(document.sections),
            'section_types': section_counts,
            'contact_count': len(contacts),
            'vendor_count': len(vendors),
            'avg_section_length': sum(s.word_count for s in document.sections) / len(document.sections) if document.sections else 0
        }

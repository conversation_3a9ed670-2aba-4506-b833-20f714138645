"""
Bot BCM Validator
Main validator that uses Mistral 7B model for intelligent BCM document analysis.
"""

import os
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

try:
    from .mistral_model import MistralBCMModel, ModelResponse
    from .bot_document_processor import BotDocumentProcessor, ProcessedDocument, DocumentSection
except ImportError:
    from mistral_model import MistralBCMModel, ModelResponse
    from bot_document_processor import BotDocumentProcessor, ProcessedDocument, DocumentSection

@dataclass
class BotValidationFinding:
    """AI-powered validation finding"""
    section_title: str
    section_type: str
    iso_requirement: str
    ai_analysis: str
    compliance_status: str
    specific_issues: List[str]
    practical_fixes: List[str]
    confidence_score: float
    processing_time: float

@dataclass
class BotValidationReport:
    """Complete bot validation report"""
    document_title: str
    validation_date: datetime
    model_used: str
    overall_assessment: str
    compliance_summary: Dict[str, int]
    findings: List[BotValidationFinding]
    contact_analysis: str
    procedure_analysis: str
    vendor_analysis: str
    recovery_analysis: str
    improvement_summary: str
    document_stats: Dict[str, Any]
    total_processing_time: float

class BotBCMValidator:
    """AI-powered BCM validator using Mistral 7B"""

    def __init__(self, model_path: str = "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf"):
        self.model = MistralBCMModel(model_path)
        self.processor = BotDocumentProcessor()
        self.iso_requirements = self._initialize_iso_requirements()

    def _initialize_iso_requirements(self) -> Dict[str, str]:
        """Initialize ISO 22301 requirements for AI analysis"""

        return {
            'policy': "ISO 22301 Clause 5.2: Business continuity policy - Top management must establish, implement and maintain a business continuity policy that includes commitment to satisfy applicable requirements and continual improvement of the BCMS.",

            'scope': "ISO 22301 Clause 4.3: Determining the scope of the BCMS - The organization must determine the boundaries and applicability of the BCMS to establish its scope, considering internal and external issues, requirements of interested parties, and products and services.",

            'objectives': "ISO 22301 Clause 6.2: Business continuity objectives and planning to achieve them - The organization must establish business continuity objectives that are measurable, monitored, communicated, and updated as appropriate.",

            'roles': "ISO 22301 Clause 5.3: Organizational roles, responsibilities and authorities - Top management must ensure that responsibilities and authorities for relevant roles are assigned, communicated and understood within the organization.",

            'bia': "ISO 22301 Clause 8.2.1: Business impact analysis - The organization must conduct and maintain a business impact analysis to identify activities that support the delivery of products and services, assess the impacts over time of not performing these activities, and set prioritized timeframes for resuming activities.",

            'risk': "ISO 22301 Clause 8.2.2: Risk assessment - The organization must establish, implement and maintain a risk assessment process to identify, analyze and evaluate the risk of disruptive incidents that can affect the organization's prioritized activities.",

            'recovery': "ISO 22301 Clause 8.4: Business continuity plans and procedures - The organization must establish business continuity plans and procedures based on the results of the business impact analysis and risk assessment to ensure it can continue to operate and/or recover its prioritized activities.",

            'testing': "ISO 22301 Clause 8.5: Exercising and testing - The organization must exercise and test its business continuity plans and procedures at planned intervals, in a manner appropriate to its size and complexity.",

            'training': "ISO 22301 Clause 7.2: Competence - The organization must determine the necessary competence of persons doing work that affects business continuity performance, ensure these persons are competent, and retain appropriate documented information as evidence of competence.",

            'audit': "ISO 22301 Clause 9.2: Internal audit - The organization must conduct internal audits at planned intervals to provide information on whether the BCMS conforms to the organization's own requirements and ISO 22301 requirements.",

            'improvement': "ISO 22301 Clause 10: Improvement - The organization must determine and select opportunities for improvement and implement necessary actions to achieve intended outcomes of the BCMS."
        }

    def validate_document(self, file_path: str) -> BotValidationReport:
        """Validate BCM document using AI analysis"""

        start_time = time.time()

        print("🤖 Starting AI-powered BCM validation...")

        # Load model
        if not self.model.load_model():
            raise RuntimeError("Failed to load Mistral 7B model")

        try:
            # Process document
            print("📄 Processing document...")
            document = self.processor.process_document(file_path)
            print(f"✅ Document processed: {document.total_pages} pages, {len(document.sections)} sections")

            # Get critical sections
            critical_sections = self.processor.get_critical_sections(document)
            print(f"🔍 Found critical sections: {list(critical_sections.keys())}")

            # Analyze each critical section
            findings = []
            print("🤖 Running AI analysis on sections...")

            for section_type, sections in critical_sections.items():
                if section_type in self.iso_requirements:
                    iso_req = self.iso_requirements[section_type]

                    for section in sections[:2]:  # Limit to first 2 sections per type
                        print(f"   Analyzing: {section.title}")
                        finding = self._analyze_section_with_ai(section, iso_req)
                        findings.append(finding)

            # Specialized analyses
            print("🔍 Running specialized analyses...")

            # Contact information analysis
            contact_analysis = self._analyze_contact_information(document)

            # Procedure clarity analysis
            procedure_analysis = self._analyze_procedures(document)

            # Vendor information analysis
            vendor_analysis = self._analyze_vendor_information(document)

            # Recovery objectives analysis
            recovery_analysis = self._analyze_recovery_objectives(document)

            # Generate improvement summary
            print("📊 Generating improvement summary...")
            improvement_summary = self._generate_improvement_summary(document, findings)

            # Calculate compliance summary
            compliance_summary = self._calculate_compliance_summary(findings)

            # Generate overall assessment
            overall_assessment = self._generate_overall_assessment(compliance_summary, len(findings))

            # Get document statistics
            doc_stats = self.processor.get_document_statistics(document)

            total_time = time.time() - start_time

            report = BotValidationReport(
                document_title=document.title,
                validation_date=datetime.now(),
                model_used="Mistral 7B Instruct",
                overall_assessment=overall_assessment,
                compliance_summary=compliance_summary,
                findings=findings,
                contact_analysis=contact_analysis,
                procedure_analysis=procedure_analysis,
                vendor_analysis=vendor_analysis,
                recovery_analysis=recovery_analysis,
                improvement_summary=improvement_summary,
                document_stats=doc_stats,
                total_processing_time=total_time
            )

            print(f"✅ AI validation complete! Total time: {total_time:.1f} seconds")
            return report

        finally:
            # Unload model to free memory
            self.model.unload_model()

    def _analyze_section_with_ai(self, section: DocumentSection, iso_requirement: str) -> BotValidationFinding:
        """Analyze a section using AI"""

        # Prepare content for analysis
        content = self.processor.prepare_content_for_analysis(section)

        # Get AI analysis
        response = self.model.analyze_bcm_section(section.title, content, iso_requirement)

        # Parse AI response
        compliance_status, issues, fixes = self._parse_ai_response(response.content)

        return BotValidationFinding(
            section_title=section.title,
            section_type=section.section_type,
            iso_requirement=iso_requirement,
            ai_analysis=response.content,
            compliance_status=compliance_status,
            specific_issues=issues,
            practical_fixes=fixes,
            confidence_score=response.confidence,
            processing_time=response.processing_time
        )

    def _parse_ai_response(self, ai_response: str) -> tuple:
        """Parse AI response to extract structured information"""

        # Default values
        compliance_status = "Unknown"
        issues = []
        fixes = []

        try:
            lines = ai_response.split('\n')
            current_section = None

            for line in lines:
                line = line.strip()

                # Identify sections
                if 'COMPLIANCE STATUS' in line.upper():
                    current_section = 'status'
                    # Extract status from the line
                    if 'compliant' in line.lower():
                        if 'non-compliant' in line.lower():
                            compliance_status = "Non-Compliant"
                        elif 'partially' in line.lower():
                            compliance_status = "Partially Compliant"
                        else:
                            compliance_status = "Compliant"
                elif 'SPECIFIC ISSUES' in line.upper():
                    current_section = 'issues'
                elif 'PRACTICAL FIXES' in line.upper():
                    current_section = 'fixes'
                elif line and not line.startswith('#'):
                    # Add content to current section
                    if current_section == 'issues' and line.startswith(('-', '•', '*')):
                        issues.append(line[1:].strip())
                    elif current_section == 'fixes' and line.startswith(('-', '•', '*')):
                        fixes.append(line[1:].strip())
                    elif current_section == 'issues' and len(line) > 10:
                        issues.append(line)
                    elif current_section == 'fixes' and len(line) > 10:
                        fixes.append(line)

        except Exception as e:
            print(f"Warning: Error parsing AI response: {e}")

        return compliance_status, issues[:5], fixes[:5]  # Limit to 5 items each

    def _analyze_contact_information(self, document: ProcessedDocument) -> str:
        """Analyze contact information using AI"""

        # Get contact-related sections
        contact_sections = self.processor.get_sections_by_type(document, 'contact')
        role_sections = self.processor.get_sections_by_type(document, 'roles')

        # Combine content
        combined_content = ""
        for sections in [contact_sections, role_sections]:
            for section in sections[:2]:  # Limit sections
                combined_content += f"{section.title}: {section.content[:500]}\n\n"

        if not combined_content:
            return "No contact information sections found in the document."

        response = self.model.validate_contact_information(combined_content)
        return response.content

    def _analyze_procedures(self, document: ProcessedDocument) -> str:
        """Analyze procedure clarity using AI"""

        procedure_sections = self.processor.get_sections_by_type(document, 'procedure')

        combined_content = ""
        for section in procedure_sections[:3]:  # Limit to 3 sections
            combined_content += f"{section.title}: {section.content[:500]}\n\n"

        if not combined_content:
            return "No procedure sections found in the document."

        response = self.model.analyze_procedures_clarity(combined_content)
        return response.content

    def _analyze_vendor_information(self, document: ProcessedDocument) -> str:
        """Analyze vendor information using AI"""

        vendor_sections = self.processor.get_sections_by_type(document, 'vendor')

        combined_content = ""
        for section in vendor_sections[:2]:  # Limit sections
            combined_content += f"{section.title}: {section.content[:500]}\n\n"

        if not combined_content:
            return "No vendor information sections found in the document."

        response = self.model.evaluate_vendor_information(combined_content)
        return response.content

    def _analyze_recovery_objectives(self, document: ProcessedDocument) -> str:
        """Analyze recovery objectives using AI"""

        recovery_sections = self.processor.get_sections_by_type(document, 'recovery')
        bia_sections = self.processor.get_sections_by_type(document, 'bia')

        combined_content = ""
        for sections in [recovery_sections, bia_sections]:
            for section in sections[:2]:  # Limit sections
                combined_content += f"{section.title}: {section.content[:500]}\n\n"

        if not combined_content:
            return "No recovery or BIA sections found in the document."

        response = self.model.check_recovery_objectives(combined_content)
        return response.content

    def _generate_improvement_summary(self, document: ProcessedDocument, findings: List[BotValidationFinding]) -> str:
        """Generate overall improvement summary using AI"""

        # Extract key findings
        key_findings = []
        for finding in findings:
            if finding.compliance_status in ["Non-Compliant", "Partially Compliant"]:
                key_findings.extend(finding.specific_issues[:2])

        response = self.model.generate_improvement_summary(document.title, key_findings[:10])
        return response.content

    def _calculate_compliance_summary(self, findings: List[BotValidationFinding]) -> Dict[str, int]:
        """Calculate compliance summary statistics"""

        summary = {
            "Compliant": 0,
            "Partially Compliant": 0,
            "Non-Compliant": 0,
            "Unknown": 0
        }

        for finding in findings:
            status = finding.compliance_status
            if status in summary:
                summary[status] += 1
            else:
                summary["Unknown"] += 1

        return summary

    def _generate_overall_assessment(self, compliance_summary: Dict[str, int], total_findings: int) -> str:
        """Generate overall assessment"""

        if total_findings == 0:
            return "🔴 INSUFFICIENT DATA - Unable to assess compliance due to lack of analyzable content"

        compliant = compliance_summary.get("Compliant", 0)
        partial = compliance_summary.get("Partially Compliant", 0)
        non_compliant = compliance_summary.get("Non-Compliant", 0)

        compliance_rate = compliant / total_findings if total_findings > 0 else 0

        if compliance_rate >= 0.8:
            return "🟢 GOOD - Document demonstrates strong compliance with ISO 22301 requirements"
        elif compliance_rate >= 0.6:
            return "🟡 FAIR - Document shows reasonable compliance but needs improvements"
        elif compliance_rate >= 0.4:
            return "🟠 POOR - Document has significant gaps requiring attention"
        else:
            return "🔴 CRITICAL - Document needs substantial work to meet ISO 22301 standards"

    def generate_detailed_report(self, report: BotValidationReport, output_file: str = None) -> str:
        """Generate detailed AI validation report"""

        report_text = f"""
🤖 AI-POWERED BCM VALIDATION REPORT
==================================

Document: {report.document_title}
Validation Date: {report.validation_date.strftime('%B %d, %Y at %H:%M')}
AI Model: {report.model_used}
Processing Time: {report.total_processing_time:.1f} seconds

📊 OVERALL ASSESSMENT
====================
{report.overall_assessment}

📈 COMPLIANCE SUMMARY
====================
"""

        total_analyzed = sum(report.compliance_summary.values())
        for status, count in report.compliance_summary.items():
            percentage = (count / total_analyzed * 100) if total_analyzed > 0 else 0
            report_text += f"• {status}: {count} sections ({percentage:.1f}%)\n"

        # Document statistics
        report_text += f"""

📋 DOCUMENT STATISTICS
=====================
• Total Pages: {report.document_stats['total_pages']}
• Total Words: {report.document_stats['total_words']:,}
• Total Sections: {report.document_stats['total_sections']}
• Contacts Found: {report.document_stats['contact_count']}
• Vendors Found: {report.document_stats['vendor_count']}
• Average Section Length: {report.document_stats['avg_section_length']:.0f} words

"""

        # AI Analysis Results
        report_text += f"""
🤖 AI ANALYSIS RESULTS
=====================

📞 CONTACT INFORMATION ANALYSIS:
{report.contact_analysis}

📝 PROCEDURE CLARITY ANALYSIS:
{report.procedure_analysis}

🏢 VENDOR INFORMATION ANALYSIS:
{report.vendor_analysis}

⏰ RECOVERY OBJECTIVES ANALYSIS:
{report.recovery_analysis}

"""

        # Detailed findings
        if report.findings:
            report_text += f"""
🔍 DETAILED SECTION ANALYSIS
===========================
"""

            for i, finding in enumerate(report.findings, 1):
                report_text += f"""
{i}. {finding.section_title} ({finding.section_type.upper()})
   ISO Requirement: {finding.iso_requirement.split(':')[1].split('-')[0].strip() if ':' in finding.iso_requirement else 'General'}
   Compliance Status: {finding.compliance_status}
   AI Confidence: {finding.confidence_score:.1f}
   Processing Time: {finding.processing_time:.1f}s

   Specific Issues Found:
"""
                for issue in finding.specific_issues:
                    report_text += f"   • {issue}\n"

                report_text += f"\n   Recommended Fixes:\n"
                for fix in finding.practical_fixes:
                    report_text += f"   • {fix}\n"

                report_text += "\n"

        # Improvement summary
        report_text += f"""
💡 AI IMPROVEMENT RECOMMENDATIONS
================================
{report.improvement_summary}

🎯 NEXT STEPS
============
1. Review the AI analysis results above
2. Focus on sections marked as "Non-Compliant" first
3. Implement the specific fixes recommended by the AI
4. Address contact information and procedure clarity issues
5. Ensure vendor information is complete and current
6. Define specific recovery time objectives
7. Re-run this AI validation after making improvements

📝 NOTE: This analysis was performed by AI and should be reviewed by BCM experts for accuracy and completeness.
"""

        # Save to file if requested
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📁 AI validation report saved to: {output_file}")

        return report_text

    def generate_executive_summary(self, report: BotValidationReport, output_file: str = None) -> str:
        """Generate executive summary for management"""

        total_analyzed = sum(report.compliance_summary.values())
        compliant_rate = (report.compliance_summary.get("Compliant", 0) / total_analyzed * 100) if total_analyzed > 0 else 0

        summary_text = f"""
📊 EXECUTIVE SUMMARY - AI BCM VALIDATION
=======================================

Document: {report.document_title}
Assessment Date: {report.validation_date.strftime('%B %d, %Y')}
AI Model Used: {report.model_used}

🎯 KEY FINDINGS
==============
Overall Assessment: {report.overall_assessment}
Compliance Rate: {compliant_rate:.1f}% of analyzed sections fully compliant
Sections Analyzed: {total_analyzed}
Processing Time: {report.total_processing_time:.1f} seconds

📈 COMPLIANCE BREAKDOWN
======================
"""

        for status, count in report.compliance_summary.items():
            percentage = (count / total_analyzed * 100) if total_analyzed > 0 else 0
            if count > 0:
                summary_text += f"• {status}: {count} sections ({percentage:.1f}%)\n"

        # Extract key issues from improvement summary
        summary_text += f"""

🚨 PRIORITY ACTIONS REQUIRED
============================
Based on AI analysis, the following areas require immediate attention:

{report.improvement_summary.split('TOP 3 PRIORITIES:')[1].split('QUICK WINS:')[0] if 'TOP 3 PRIORITIES:' in report.improvement_summary else 'See detailed report for specific recommendations.'}

💰 BUSINESS IMPACT
==================
• Incomplete BCM documentation increases business risk during disruptions
• Missing contact information could delay emergency response
• Unclear procedures may lead to ineffective crisis management
• Non-compliance with ISO 22301 may affect certification and customer confidence

📅 RECOMMENDED TIMELINE
======================
• Immediate (0-30 days): Address critical compliance gaps identified by AI
• Short-term (30-90 days): Implement procedure improvements and contact updates
• Medium-term (90-180 days): Complete comprehensive document review and testing
• Ongoing: Regular AI-powered validation and continuous improvement

This AI-powered analysis provides objective, consistent evaluation of BCM documentation compliance with ISO 22301 standards.
"""

        # Save to file if requested
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(summary_text)
            print(f"📁 Executive summary saved to: {output_file}")

        return summary_text

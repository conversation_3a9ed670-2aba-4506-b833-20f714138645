#!/usr/bin/env python3
"""
Test script for Bot BCM Validator
Tests the AI-powered validation system.
"""

import os
import sys

def test_model_availability():
    """Test if Mistral model is available"""

    print("🧪 Testing Mistral 7B Model Availability")
    print("=" * 40)

    model_paths = [
        "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        "mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    ]

    model_found = False
    for path in model_paths:
        if os.path.exists(path):
            print(f"✅ Found model: {path}")
            model_found = True

            # Test model loading
            try:
                try:
                    from bot_validation.mistral_model import MistralBCMModel
                except ImportError:
                    sys.path.append('bot_validation')
                    from mistral_model import MistralBCMModel
                model = MistralBCMModel(path)
                print("✅ Model class initialized")

                # Try to load (this might take time)
                print("🔄 Testing model loading (this may take a moment)...")
                if model.load_model():
                    print("✅ Model loaded successfully")

                    # Test simple generation
                    response = model.generate_response("Hello, this is a test.", max_tokens=50)
                    print(f"✅ Model response test: {len(response.content)} characters generated")

                    model.unload_model()
                    print("✅ Model unloaded successfully")
                else:
                    print("❌ Model failed to load")

            except Exception as e:
                print(f"❌ Model test failed: {e}")

            break

    if not model_found:
        print("❌ No Mistral 7B model found")
        print("Please download the model from:")
        print("https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF")
        print("And place it in one of these locations:")
        for path in model_paths:
            print(f"  • {path}")

    return model_found

def test_document_processor():
    """Test document processor"""

    print("\n🧪 Testing Document Processor")
    print("=" * 40)

    try:
        try:
            from bot_validation.bot_document_processor import BotDocumentProcessor
        except ImportError:
            sys.path.append('bot_validation')
            from bot_document_processor import BotDocumentProcessor

        processor = BotDocumentProcessor()
        print("✅ Document processor initialized")

        # Test with BCM document if available
        pdf_path = "BCM Plan - Ptech - 19082024.pdf"
        if os.path.exists(pdf_path):
            print(f"📄 Testing with document: {pdf_path}")

            document = processor.process_document(pdf_path)
            print(f"✅ Document processed successfully")
            print(f"   Title: {document.title}")
            print(f"   Pages: {document.total_pages}")
            print(f"   Words: {document.total_words:,}")
            print(f"   Sections: {len(document.sections)}")

            # Test section classification
            critical_sections = processor.get_critical_sections(document)
            print(f"✅ Critical sections identified: {list(critical_sections.keys())}")

            # Test contact extraction
            contacts = processor.extract_contact_information(document)
            print(f"✅ Contacts extracted: {len(contacts)} found")

            # Test vendor extraction
            vendors = processor.extract_vendor_information(document)
            print(f"✅ Vendors extracted: {len(vendors)} found")

        else:
            print(f"⚠️  Test document not found: {pdf_path}")
            print("✅ Document processor class works (no document to test)")

        return True

    except Exception as e:
        print(f"❌ Document processor test failed: {e}")
        return False

def test_bot_validator():
    """Test bot validator (without model loading)"""

    print("\n🧪 Testing Bot Validator")
    print("=" * 40)

    try:
        try:
            from bot_validation.bot_bcm_validator import BotBCMValidator
        except ImportError:
            sys.path.append('bot_validation')
            from bot_bcm_validator import BotBCMValidator

        # Initialize without loading model
        validator = BotBCMValidator()
        print("✅ Bot validator initialized")

        # Test ISO requirements
        iso_reqs = validator._initialize_iso_requirements()
        print(f"✅ ISO requirements loaded: {len(iso_reqs)} requirements")

        # Show sample requirement
        if 'policy' in iso_reqs:
            print(f"✅ Sample requirement (policy): {iso_reqs['policy'][:100]}...")

        return True

    except Exception as e:
        print(f"❌ Bot validator test failed: {e}")
        return False

def test_full_validation():
    """Test full validation if model and document are available"""

    print("\n🧪 Testing Full AI Validation")
    print("=" * 40)

    # Check prerequisites
    pdf_path = "BCM Plan - Ptech - 19082024.pdf"
    if not os.path.exists(pdf_path):
        print(f"⚠️  Test document not found: {pdf_path}")
        return False

    model_paths = [
        "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        "mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    ]

    model_path = None
    for path in model_paths:
        if os.path.exists(path):
            model_path = path
            break

    if not model_path:
        print("⚠️  Mistral model not found - skipping full validation test")
        return False

    try:
        try:
            from bot_validation.bot_bcm_validator import BotBCMValidator
        except ImportError:
            sys.path.append('bot_validation')
            from bot_bcm_validator import BotBCMValidator

        print(f"📄 Document: {pdf_path}")
        print(f"🤖 Model: {model_path}")
        print("🔄 Running full AI validation (this will take several minutes)...")

        validator = BotBCMValidator(model_path)
        report = validator.validate_document(pdf_path)

        print("✅ Full validation completed!")
        print(f"   Document: {report.document_title}")
        print(f"   Assessment: {report.overall_assessment}")
        print(f"   Findings: {len(report.findings)}")
        print(f"   Processing Time: {report.total_processing_time:.1f} seconds")

        # Test report generation
        test_report = validator.generate_detailed_report(report, "test_ai_report.txt")
        print("✅ Detailed report generated")

        test_summary = validator.generate_executive_summary(report, "test_ai_summary.txt")
        print("✅ Executive summary generated")

        return True

    except Exception as e:
        print(f"❌ Full validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""

    print("🤖 Bot BCM Validator - Test Suite")
    print("=" * 50)

    tests = [
        ("Document Processor", test_document_processor),
        ("Bot Validator", test_bot_validator),
        ("Mistral Model", test_model_availability),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - CRASHED: {e}")

    print(f"\n📊 Basic Test Results: {passed}/{total} tests passed")

    # Optional full validation test
    if passed == total:
        print("\n🚀 All basic tests passed!")

        run_full = input("\nRun full AI validation test? (requires model and document) [y/N]: ").strip().lower()
        if run_full in ['y', 'yes']:
            if test_full_validation():
                print("🎉 Full AI validation test passed!")
            else:
                print("⚠️  Full AI validation test failed or skipped")

    print(f"\n🎯 To run AI validation on your BCM document:")
    print("   python bot_validation/main.py your_document.pdf")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Simple BCM validation test without LLM dependencies
"""

import os
import sys

def test_bcm_validation():
    """Test BCM validation functionality"""
    
    print("🏢 BCM Expert - Simple Validation Test")
    print("=" * 50)
    
    # Check if BCM document exists
    pdf_path = "BCM Plan - Ptech - 19082024.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ BCM document not found: {pdf_path}")
        return False
    
    print(f"📄 Found BCM document: {pdf_path}")
    
    try:
        # Import BCM Expert modules
        from bcm_expert import BCMExpert
        print("✅ BCM Expert modules imported successfully")
        
        # Initialize BCM Expert
        bcm_expert = BCMExpert()
        print("✅ BCM Expert initialized")
        
        # Test document analysis first
        print("\n🔍 Step 1: Analyzing document structure...")
        try:
            analysis_result = bcm_expert.analyze_document_structure(pdf_path)
            summary = analysis_result['summary']
            
            print(f"📊 Document Analysis Results:")
            print(f"  Title: {summary['title']}")
            print(f"  Pages: {summary['total_pages']}")
            print(f"  Word Count: {summary['word_count']:,}")
            print(f"  Sections: {summary['section_count']}")
            
            if summary['top_keywords']:
                print(f"  Top Keywords: {', '.join(summary['top_keywords'][:5])}")
            
            print("✅ Document analysis completed successfully")
            
        except Exception as e:
            print(f"❌ Document analysis failed: {e}")
            return False
        
        # Test full validation
        print("\n🔍 Step 2: Running ISO 22301 compliance validation...")
        try:
            # Create output directory
            os.makedirs('validation_reports', exist_ok=True)
            
            validation_result = bcm_expert.validate_document(pdf_path, output_dir='validation_reports')
            
            print(f"\n📊 Validation Results:")
            print(f"  Overall Compliance Score: {validation_result['summary']['overall_score']:.1f}%")
            print(f"  Total Requirements: {validation_result['summary']['total_requirements']}")
            print(f"  Critical Gaps: {validation_result['summary']['critical_gaps']}")
            print(f"  Recommendations: {validation_result['summary']['recommendations']}")
            
            # Show compliance level
            score = validation_result['summary']['overall_score']
            if score >= 80:
                print("\n✅ GOOD - Strong compliance with ISO 22301")
            elif score >= 60:
                print("\n⚠️  MODERATE - Reasonable compliance, improvements needed")
            elif score >= 40:
                print("\n❌ POOR - Significant gaps require attention")
            else:
                print("\n🚨 CRITICAL - Major compliance deficiencies")
            
            # Show some sample results
            compliance_report = validation_result['compliance_report']
            
            print(f"\n📋 Sample Validation Details:")
            
            # Show first few validation results
            for i, result in enumerate(compliance_report.validation_results[:3]):
                print(f"  {i+1}. Clause {result.clause}: {result.title}")
                print(f"     Status: {result.compliance_level.value} (Score: {result.score:.1f}%)")
                if result.gaps:
                    print(f"     Gaps: {result.gaps[0]}")
            
            if len(compliance_report.validation_results) > 3:
                print(f"     ... and {len(compliance_report.validation_results) - 3} more requirements")
            
            # Show critical gaps
            if compliance_report.critical_gaps:
                print(f"\n🚨 Critical Gaps:")
                for gap in compliance_report.critical_gaps[:3]:
                    print(f"  • {gap}")
            
            # Show recommendations
            if compliance_report.recommendations:
                print(f"\n💡 Top Recommendations:")
                for rec in compliance_report.recommendations[:3]:
                    print(f"  • {rec}")
            
            print(f"\n📁 Detailed reports saved to: validation_reports/")
            
            # List generated files
            if 'output_files' in validation_result:
                print(f"\n📊 Generated Report Files:")
                for report_type, filename in validation_result['output_files'].items():
                    if os.path.exists(filename):
                        print(f"  • {report_type.upper()}: {filename}")
            
            print("\n✅ BCM validation completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Validation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except ImportError as e:
        print(f"❌ Failed to import BCM Expert modules: {e}")
        print("Make sure all required dependencies are installed.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_iso_standards_info():
    """Show ISO 22301 standards information"""
    try:
        from bcm_expert import BCMExpert
        
        bcm_expert = BCMExpert()
        info = bcm_expert.get_iso_standards_info()
        
        print(f"\n📋 ISO 22301:2019 Standards Information:")
        print(f"  Total Requirements: {info['total_requirements']}")
        print(f"  Mandatory Documents: {len(info['mandatory_documents'])}")
        print(f"  Clause Groups: {len(info['clause_groups'])}")
        
        print(f"\n📚 Clause Groups:")
        for clause, title in info['clause_groups'].items():
            print(f"  • Clause {clause}: {title}")
        
        print(f"\n📄 Sample Mandatory Documents:")
        for doc in info['mandatory_documents'][:5]:
            print(f"  • {doc}")
        
        if len(info['mandatory_documents']) > 5:
            print(f"  ... and {len(info['mandatory_documents']) - 5} more")
        
    except Exception as e:
        print(f"❌ Error showing standards info: {e}")

def main():
    """Main function"""
    
    print("🎯 Choose an option:")
    print("1. Run BCM document validation")
    print("2. Show ISO 22301 standards information")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        success = test_bcm_validation()
        if success:
            print("\n🎉 Validation test completed successfully!")
        else:
            print("\n❌ Validation test failed.")
    elif choice == "2":
        show_iso_standards_info()
    elif choice == "3":
        print("👋 Exiting. Thank you!")
    else:
        print("❌ Invalid choice.")

if __name__ == "__main__":
    main()

"""
Validation Report Generator
Generates detailed compliance reports with visualizations and actionable insights.
"""

import json
import csv
from typing import Dict, List, Any
from datetime import datetime
from dataclasses import asdict

# Optional imports for visualization
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

from bcm_validator import ComplianceReport, ValidationResult, ComplianceLevel

class ValidationReportGenerator:
    """Generates comprehensive validation reports"""

    def __init__(self):
        self.setup_plotting()

    def setup_plotting(self):
        """Setup plotting style"""
        if MATPLOTLIB_AVAILABLE:
            plt.style.use('default')
            if 'sns' in globals():
                sns.set_palette("husl")

    def generate_executive_summary(self, report: ComplianceReport) -> str:
        """Generate executive summary of the compliance report"""

        total_requirements = len(report.validation_results)
        compliant_count = report.compliance_summary[ComplianceLevel.FULLY_COMPLIANT.value]
        partial_count = report.compliance_summary[ComplianceLevel.PARTIALLY_COMPLIANT.value]
        non_compliant_count = report.compliance_summary[ComplianceLevel.NON_COMPLIANT.value]

        compliance_percentage = (compliant_count / total_requirements) * 100 if total_requirements > 0 else 0

        summary = f"""
EXECUTIVE SUMMARY
================

Document: {report.document_title}
Assessment Date: {report.validation_date.strftime('%B %d, %Y')}
Overall Compliance Score: {report.overall_score:.1f}%

KEY FINDINGS:
- Total Requirements Assessed: {total_requirements}
- Fully Compliant: {compliant_count} ({compliance_percentage:.1f}%)
- Partially Compliant: {partial_count}
- Non-Compliant: {non_compliant_count}
- Critical Gaps Identified: {len(report.critical_gaps)}

COMPLIANCE STATUS:
"""

        if report.overall_score >= 80:
            summary += "✅ GOOD - The document demonstrates strong compliance with ISO 22301 standards.\n"
        elif report.overall_score >= 60:
            summary += "⚠️  MODERATE - The document shows reasonable compliance but requires improvements.\n"
        elif report.overall_score >= 40:
            summary += "❌ POOR - Significant gaps exist that need immediate attention.\n"
        else:
            summary += "🚨 CRITICAL - Major compliance deficiencies require comprehensive remediation.\n"

        summary += f"""
PRIORITY ACTIONS:
"""

        # Add top 3 recommendations
        for i, rec in enumerate(report.recommendations[:3], 1):
            summary += f"{i}. {rec}\n"

        if report.critical_gaps:
            summary += f"""
CRITICAL GAPS REQUIRING IMMEDIATE ATTENTION:
"""
            for gap in report.critical_gaps[:3]:
                summary += f"• {gap}\n"

        return summary

    def generate_detailed_analysis(self, report: ComplianceReport) -> str:
        """Generate detailed analysis by ISO 22301 clause groups"""

        analysis = """
DETAILED ANALYSIS BY ISO 22301 CLAUSE GROUPS
===========================================

"""

        # Group results by clause prefix
        clause_groups = {
            '4': 'Context of the Organization',
            '5': 'Leadership',
            '6': 'Planning',
            '7': 'Support',
            '8': 'Operation',
            '9': 'Performance Evaluation',
            '10': 'Improvement'
        }

        for prefix, title in clause_groups.items():
            group_results = [r for r in report.validation_results if r.clause.startswith(prefix)]

            if not group_results:
                continue

            # Calculate group statistics
            group_scores = [r.score for r in group_results]
            avg_score = sum(group_scores) / len(group_scores) if group_scores else 0

            compliant = len([r for r in group_results if r.compliance_level == ComplianceLevel.FULLY_COMPLIANT])
            partial = len([r for r in group_results if r.compliance_level == ComplianceLevel.PARTIALLY_COMPLIANT])
            non_compliant = len([r for r in group_results if r.compliance_level == ComplianceLevel.NON_COMPLIANT])

            analysis += f"""
Clause {prefix}: {title}
{'-' * (len(title) + 10)}
Average Score: {avg_score:.1f}%
Requirements: {len(group_results)} | Compliant: {compliant} | Partial: {partial} | Non-Compliant: {non_compliant}

"""

            # Add specific findings for non-compliant items
            non_compliant_items = [r for r in group_results if r.compliance_level == ComplianceLevel.NON_COMPLIANT]
            if non_compliant_items:
                analysis += "Non-Compliant Requirements:\n"
                for item in non_compliant_items:
                    analysis += f"  • {item.clause}: {item.title}\n"
                    if item.gaps:
                        analysis += f"    Gaps: {'; '.join(item.gaps[:2])}\n"
                analysis += "\n"

        return analysis

    def generate_gap_analysis(self, report: ComplianceReport) -> str:
        """Generate gap analysis with prioritized action items"""

        gap_analysis = """
GAP ANALYSIS AND ACTION PLAN
============================

"""

        # Categorize gaps by priority
        critical_gaps = []
        high_priority_gaps = []
        medium_priority_gaps = []

        for result in report.validation_results:
            if result.compliance_level == ComplianceLevel.NON_COMPLIANT:
                if result.clause in ['4.3', '5.2', '6.2', '8.2', '8.4']:  # Critical clauses
                    critical_gaps.append(result)
                elif result.score < 30:
                    high_priority_gaps.append(result)
                else:
                    medium_priority_gaps.append(result)

        # Critical gaps
        if critical_gaps:
            gap_analysis += """
CRITICAL GAPS (Immediate Action Required):
"""
            for gap in critical_gaps:
                gap_analysis += f"""
• {gap.clause}: {gap.title}
  Current Score: {gap.score:.1f}%
  Key Issues: {'; '.join(gap.gaps[:3])}
  Recommended Actions: {'; '.join(gap.recommendations[:2])}

"""

        # High priority gaps
        if high_priority_gaps:
            gap_analysis += """
HIGH PRIORITY GAPS (Address within 30 days):
"""
            for gap in high_priority_gaps:
                gap_analysis += f"""
• {gap.clause}: {gap.title}
  Current Score: {gap.score:.1f}%
  Key Issues: {'; '.join(gap.gaps[:2])}

"""

        # Medium priority gaps
        if medium_priority_gaps:
            gap_analysis += """
MEDIUM PRIORITY GAPS (Address within 90 days):
"""
            for gap in medium_priority_gaps:
                gap_analysis += f"• {gap.clause}: {gap.title} (Score: {gap.score:.1f}%)\n"

        return gap_analysis

    def generate_compliance_matrix(self, report: ComplianceReport):
        """Generate compliance matrix as DataFrame or dict"""

        data = []
        for result in report.validation_results:
            data.append({
                'Clause': result.clause,
                'Title': result.title,
                'Compliance Level': result.compliance_level.value,
                'Score': result.score,
                'Findings Count': len(result.findings),
                'Gaps Count': len(result.gaps),
                'Recommendations Count': len(result.recommendations)
            })

        if PANDAS_AVAILABLE:
            return pd.DataFrame(data)
        else:
            return data

    def create_compliance_charts(self, report: ComplianceReport, output_dir: str = '.'):
        """Create compliance visualization charts"""

        if not MATPLOTLIB_AVAILABLE:
            print("Warning: Matplotlib not available. Skipping chart generation.")
            return

        # 1. Overall compliance pie chart
        plt.figure(figsize=(10, 6))

        plt.subplot(1, 2, 1)
        labels = list(report.compliance_summary.keys())
        sizes = list(report.compliance_summary.values())
        colors = ['#28a745', '#ffc107', '#dc3545', '#6c757d']

        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('Overall Compliance Distribution')

        # 2. Compliance by clause group
        plt.subplot(1, 2, 2)

        clause_groups = {}
        for result in report.validation_results:
            group = result.clause.split('.')[0]
            if group not in clause_groups:
                clause_groups[group] = {'compliant': 0, 'partial': 0, 'non_compliant': 0}

            if result.compliance_level == ComplianceLevel.FULLY_COMPLIANT:
                clause_groups[group]['compliant'] += 1
            elif result.compliance_level == ComplianceLevel.PARTIALLY_COMPLIANT:
                clause_groups[group]['partial'] += 1
            elif result.compliance_level == ComplianceLevel.NON_COMPLIANT:
                clause_groups[group]['non_compliant'] += 1

        groups = list(clause_groups.keys())
        compliant = [clause_groups[g]['compliant'] for g in groups]
        partial = [clause_groups[g]['partial'] for g in groups]
        non_compliant = [clause_groups[g]['non_compliant'] for g in groups]

        x = range(len(groups))
        width = 0.25

        plt.bar([i - width for i in x], compliant, width, label='Compliant', color='#28a745')
        plt.bar(x, partial, width, label='Partial', color='#ffc107')
        plt.bar([i + width for i in x], non_compliant, width, label='Non-Compliant', color='#dc3545')

        plt.xlabel('ISO 22301 Clause Groups')
        plt.ylabel('Number of Requirements')
        plt.title('Compliance by Clause Group')
        plt.xticks(x, [f'Clause {g}' for g in groups])
        plt.legend()

        plt.tight_layout()
        plt.savefig(f'{output_dir}/compliance_charts.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 3. Score distribution histogram
        plt.figure(figsize=(10, 6))

        scores = [result.score for result in report.validation_results if result.compliance_level != ComplianceLevel.NOT_APPLICABLE]

        plt.hist(scores, bins=20, color='skyblue', alpha=0.7, edgecolor='black')
        plt.axvline(report.overall_score, color='red', linestyle='--', linewidth=2, label=f'Overall Score: {report.overall_score:.1f}%')
        plt.xlabel('Compliance Score (%)')
        plt.ylabel('Number of Requirements')
        plt.title('Distribution of Compliance Scores')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/score_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()

    def export_to_excel(self, report: ComplianceReport, filename: str):
        """Export compliance report to Excel with multiple sheets"""

        if not PANDAS_AVAILABLE:
            print("Warning: Pandas not available. Skipping Excel export.")
            return

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Summary sheet
            summary_data = {
                'Metric': ['Document Title', 'Assessment Date', 'Overall Score', 'Total Requirements',
                          'Fully Compliant', 'Partially Compliant', 'Non-Compliant', 'Not Applicable'],
                'Value': [
                    report.document_title,
                    report.validation_date.strftime('%Y-%m-%d'),
                    f"{report.overall_score:.1f}%",
                    len(report.validation_results),
                    report.compliance_summary[ComplianceLevel.FULLY_COMPLIANT.value],
                    report.compliance_summary[ComplianceLevel.PARTIALLY_COMPLIANT.value],
                    report.compliance_summary[ComplianceLevel.NON_COMPLIANT.value],
                    report.compliance_summary[ComplianceLevel.NOT_APPLICABLE.value]
                ]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='Summary', index=False)

            # Detailed results sheet
            compliance_matrix = self.generate_compliance_matrix(report)
            compliance_matrix.to_excel(writer, sheet_name='Detailed Results', index=False)

            # Gaps and recommendations sheet
            gaps_data = []
            for result in report.validation_results:
                if result.gaps or result.recommendations:
                    gaps_data.append({
                        'Clause': result.clause,
                        'Title': result.title,
                        'Compliance Level': result.compliance_level.value,
                        'Score': result.score,
                        'Gaps': '; '.join(result.gaps),
                        'Recommendations': '; '.join(result.recommendations)
                    })

            if gaps_data:
                pd.DataFrame(gaps_data).to_excel(writer, sheet_name='Gaps & Recommendations', index=False)

            # Mandatory documents check
            mandatory_docs_data = []
            for doc, found in report.mandatory_documents_check.items():
                mandatory_docs_data.append({
                    'Document': doc,
                    'Found': 'Yes' if found else 'No',
                    'Status': '✅' if found else '❌'
                })

            pd.DataFrame(mandatory_docs_data).to_excel(writer, sheet_name='Mandatory Documents', index=False)

    def export_to_csv(self, report: ComplianceReport, filename: str):
        """Export compliance matrix to CSV"""
        compliance_matrix = self.generate_compliance_matrix(report)

        if PANDAS_AVAILABLE and hasattr(compliance_matrix, 'to_csv'):
            compliance_matrix.to_csv(filename, index=False)
        else:
            # Fallback to manual CSV writing
            import csv
            if isinstance(compliance_matrix, list) and compliance_matrix:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = compliance_matrix[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(compliance_matrix)

    def generate_action_plan(self, report: ComplianceReport) -> str:
        """Generate prioritized action plan"""

        action_plan = f"""
ISO 22301 COMPLIANCE ACTION PLAN
===============================

Document: {report.document_title}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Current Compliance Score: {report.overall_score:.1f}%

"""

        # Immediate actions (0-30 days)
        immediate_actions = []
        short_term_actions = []
        long_term_actions = []

        for result in report.validation_results:
            if result.compliance_level == ComplianceLevel.NON_COMPLIANT:
                if result.clause in ['4.3', '5.2', '8.4']:  # Critical clauses
                    immediate_actions.extend(result.recommendations)
                elif result.score < 30:
                    short_term_actions.extend(result.recommendations)
                else:
                    long_term_actions.extend(result.recommendations)

        if immediate_actions:
            action_plan += """
IMMEDIATE ACTIONS (0-30 days) - CRITICAL:
"""
            for i, action in enumerate(immediate_actions[:5], 1):
                action_plan += f"{i}. {action}\n"

        if short_term_actions:
            action_plan += """
SHORT-TERM ACTIONS (30-90 days) - HIGH PRIORITY:
"""
            for i, action in enumerate(short_term_actions[:5], 1):
                action_plan += f"{i}. {action}\n"

        if long_term_actions:
            action_plan += """
LONG-TERM ACTIONS (90+ days) - MEDIUM PRIORITY:
"""
            for i, action in enumerate(long_term_actions[:5], 1):
                action_plan += f"{i}. {action}\n"

        # Add resource requirements
        action_plan += """
ESTIMATED RESOURCE REQUIREMENTS:
• Project Manager: 0.5 FTE for 6 months
• Subject Matter Expert: 0.25 FTE for 12 months
• Training Budget: $10,000 - $25,000
• External Consultant: 20-40 days (optional)
• Documentation Tools: $2,000 - $5,000

SUCCESS METRICS:
• Target Compliance Score: 85%+
• All Critical Gaps Addressed: 100%
• Staff Training Completion: 90%+
• Management Review Frequency: Quarterly
"""

        return action_plan

    def generate_comprehensive_report(self, report: ComplianceReport, output_dir: str = '.') -> Dict[str, str]:
        """Generate comprehensive report with all components"""

        outputs = {}

        # Generate text reports
        outputs['executive_summary'] = self.generate_executive_summary(report)
        outputs['detailed_analysis'] = self.generate_detailed_analysis(report)
        outputs['gap_analysis'] = self.generate_gap_analysis(report)
        outputs['action_plan'] = self.generate_action_plan(report)

        # Save text reports
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        with open(f'{output_dir}/executive_summary_{timestamp}.txt', 'w', encoding='utf-8') as f:
            f.write(outputs['executive_summary'])

        with open(f'{output_dir}/detailed_analysis_{timestamp}.txt', 'w', encoding='utf-8') as f:
            f.write(outputs['detailed_analysis'])

        with open(f'{output_dir}/gap_analysis_{timestamp}.txt', 'w', encoding='utf-8') as f:
            f.write(outputs['gap_analysis'])

        with open(f'{output_dir}/action_plan_{timestamp}.txt', 'w', encoding='utf-8') as f:
            f.write(outputs['action_plan'])

        # Generate charts
        try:
            self.create_compliance_charts(report, output_dir)
            outputs['charts'] = f'{output_dir}/compliance_charts.png'
        except Exception as e:
            print(f"Warning: Could not generate charts: {e}")

        # Export to Excel
        try:
            excel_filename = f'{output_dir}/compliance_report_{timestamp}.xlsx'
            self.export_to_excel(report, excel_filename)
            outputs['excel'] = excel_filename
        except Exception as e:
            print(f"Warning: Could not generate Excel report: {e}")

        # Export to CSV
        try:
            csv_filename = f'{output_dir}/compliance_matrix_{timestamp}.csv'
            self.export_to_csv(report, csv_filename)
            outputs['csv'] = csv_filename
        except Exception as e:
            print(f"Warning: Could not generate CSV report: {e}")

        return outputs

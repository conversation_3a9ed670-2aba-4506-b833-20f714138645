#!/usr/bin/env python3
"""
Test script to verify BCM Expert modules work correctly
"""

def test_iso_standards():
    """Test ISO 22301 standards module"""
    print("Testing ISO 22301 standards module...")
    try:
        from iso_22301_standards import ISO22301Standards
        
        standards = ISO22301Standards()
        
        # Test basic functionality
        all_reqs = standards.get_all_requirements()
        mandatory_reqs = standards.get_mandatory_requirements()
        mandatory_docs = standards.get_mandatory_documents()
        
        print(f"✅ ISO Standards loaded: {len(all_reqs)} total requirements")
        print(f"✅ Mandatory requirements: {len(mandatory_reqs)}")
        print(f"✅ Mandatory documents: {len(mandatory_docs)}")
        
        # Test specific requirement
        req_5_2 = standards.get_requirement('5.2')
        if req_5_2:
            print(f"✅ Sample requirement (5.2): {req_5_2.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ ISO Standards test failed: {e}")
        return False

def test_document_analyzer():
    """Test document analyzer module"""
    print("\nTesting document analyzer module...")
    try:
        from document_analyzer import DocumentAnalyzer
        
        analyzer = DocumentAnalyzer()
        print("✅ Document analyzer initialized")
        
        # Test keyword extraction
        sample_text = "This is a business continuity plan for risk assessment and management review."
        keywords = analyzer.extract_keywords(sample_text)
        print(f"✅ Keyword extraction works: {len(keywords)} keywords found")
        
        return True
        
    except Exception as e:
        print(f"❌ Document analyzer test failed: {e}")
        return False

def test_bcm_validator():
    """Test BCM validator module"""
    print("\nTesting BCM validator module...")
    try:
        from bcm_validator import BCMValidator
        
        validator = BCMValidator()
        print("✅ BCM validator initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ BCM validator test failed: {e}")
        return False

def test_validation_report():
    """Test validation report module"""
    print("\nTesting validation report module...")
    try:
        from validation_report import ValidationReportGenerator
        
        report_gen = ValidationReportGenerator()
        print("✅ Validation report generator initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation report test failed: {e}")
        return False

def test_bcm_expert():
    """Test BCM expert main module"""
    print("\nTesting BCM expert main module...")
    try:
        from bcm_expert import BCMExpert
        
        expert = BCMExpert()
        print("✅ BCM expert initialized")
        
        # Test standards info
        info = expert.get_iso_standards_info()
        print(f"✅ Standards info: {info['total_requirements']} requirements")
        
        return True
        
    except Exception as e:
        print(f"❌ BCM expert test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 BCM Expert Module Tests")
    print("=" * 40)
    
    tests = [
        ("ISO 22301 Standards", test_iso_standards),
        ("Document Analyzer", test_document_analyzer),
        ("BCM Validator", test_bcm_validator),
        ("Validation Report", test_validation_report),
        ("BCM Expert", test_bcm_expert)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! BCM Expert modules are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

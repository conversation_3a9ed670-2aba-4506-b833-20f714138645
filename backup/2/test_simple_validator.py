#!/usr/bin/env python3
"""
Test script for Simple BCM Validator
"""

import os
import sys

def test_simple_validator():
    """Test the simple BCM validator"""
    
    print("🧪 Testing Simple BCM Validator")
    print("=" * 40)
    
    # Check if BCM document exists
    pdf_path = "BCM Plan - Ptech - 19082024.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ BCM document not found: {pdf_path}")
        return False
    
    try:
        # Import the simple validator
        from bcm_simple_validator import SimpleBCMValidator
        print("✅ Simple BCM Validator imported successfully")
        
        # Initialize validator
        validator = SimpleBCMValidator()
        print("✅ Validator initialized")
        
        # Validate document
        print(f"\n🔍 Validating document: {pdf_path}")
        report = validator.validate_document(pdf_path)
        
        # Display results
        print(f"\n📊 Validation Results:")
        print(f"Document: {report.document_title}")
        print(f"Status: {report.overall_status}")
        print(f"ISO Findings: {len(report.iso_findings)}")
        print(f"Practical Gaps: {len(report.practical_gaps)}")
        print(f"Critical Issues: {len(report.critical_issues)}")
        print(f"Quick Wins: {len(report.quick_wins)}")
        
        # Generate reports
        print(f"\n📄 Generating reports...")
        
        # Create output directory
        os.makedirs('simple_validation_output', exist_ok=True)
        
        # Generate main report
        main_report = validator.generate_simple_report(
            report, 
            'simple_validation_output/simple_validation_report.txt'
        )
        
        # Generate checklist
        checklist = validator.generate_checklist_report(
            report,
            'simple_validation_output/improvement_checklist.txt'
        )
        
        print("✅ Reports generated successfully!")
        
        # Show sample of critical issues
        if report.critical_issues:
            print(f"\n🚨 Sample Critical Issues:")
            for i, issue in enumerate(report.critical_issues[:3], 1):
                print(f"   {i}. {issue}")
        
        # Show sample quick wins
        if report.quick_wins:
            print(f"\n✅ Sample Quick Wins:")
            for i, win in enumerate(report.quick_wins[:3], 1):
                print(f"   {i}. {win}")
        
        print(f"\n📁 Reports saved to: simple_validation_output/")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_iso_checker():
    """Test the ISO standards checker"""
    
    print("\n🧪 Testing ISO Standards Checker")
    print("=" * 40)
    
    try:
        from bcm_simple_validator.iso_standards_checker import ISO22301SimpleChecker
        
        checker = ISO22301SimpleChecker()
        requirements = checker.get_all_requirements()
        
        print(f"✅ ISO checker loaded: {len(requirements)} requirements")
        
        # Show sample requirement
        sample_req = checker.get_requirement('5.2')
        if sample_req:
            print(f"\n📋 Sample Requirement (5.2):")
            print(f"   Title: {sample_req.title}")
            print(f"   What to look for: {sample_req.what_to_look_for[0]}")
            print(f"   Employee fix: {sample_req.employee_friendly_fix[:100]}...")
        
        # Show mandatory documents
        mandatory_docs = checker.get_mandatory_documents_checklist()
        print(f"\n📄 Mandatory documents: {len(mandatory_docs)} categories")
        
        return True
        
    except Exception as e:
        print(f"❌ ISO checker test failed: {e}")
        return False

def test_gap_analyzer():
    """Test the practical gap analyzer"""
    
    print("\n🧪 Testing Practical Gap Analyzer")
    print("=" * 40)
    
    try:
        from bcm_simple_validator.practical_gap_analyzer import PracticalGapAnalyzer
        
        analyzer = PracticalGapAnalyzer()
        print("✅ Gap analyzer initialized")
        
        # Test with sample text
        sample_text = """
        Emergency Contact: John Smith
        IT Manager: Sarah Johnson
        Vendor: TechCorp
        Recovery procedure: Contact relevant parties as soon as possible
        """
        
        gaps = analyzer.analyze_document_content(sample_text, [])
        print(f"✅ Gap analysis complete: {len(gaps)} gaps found")
        
        if gaps:
            print(f"\n🔍 Sample Gap:")
            gap = gaps[0]
            print(f"   Section: {gap.section}")
            print(f"   Problem: {gap.problem}")
            print(f"   Fix: {gap.simple_fix[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Gap analyzer test failed: {e}")
        return False

def main():
    """Run all tests"""
    
    print("🏢 Simple BCM Validator - Test Suite")
    print("=" * 50)
    
    tests = [
        ("ISO Standards Checker", test_iso_checker),
        ("Practical Gap Analyzer", test_gap_analyzer),
        ("Simple BCM Validator", test_simple_validator)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - CRASHED: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Simple BCM Validator is ready to use.")
        print("\n🚀 To validate your BCM document, run:")
        print("   python bcm_simple_validator/main.py your_document.pdf")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()

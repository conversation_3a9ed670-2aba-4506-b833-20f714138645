"""
Setup script for BCM Expert - ISO 22301 Document Validator
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="bcm-expert",
    version="1.0.0",
    author="BCM Expert Team",
    author_email="<EMAIL>",
    description="ISO 22301 Business Continuity Management Document Validator",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/bcmexpert/bcm-validator",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=[
        "PyPDF2>=3.0.0",
        "langchain>=0.1.0",
        "langchain-community>=0.0.10",
        "chromadb>=0.4.0",
        "nltk>=3.8",
        "spacy>=3.6.0",
        "pandas>=1.5.0",
        "matplotlib>=3.6.0",
        "seaborn>=0.12.0",
        "numpy>=1.24.0",
        "openpyxl>=3.1.0",
        "requests>=2.28.0",
        "beautifulsoup4>=4.11.0",
        "python-dateutil>=2.8.0",
        "tqdm>=4.64.0",
        "pydantic>=2.0.0",
    ],
    extras_require={
        "advanced": [
            "pdfplumber>=0.9.0",
            "pymupdf>=1.23.0",
            "transformers>=4.30.0",
            "sentence-transformers>=2.2.0",
        ],
        "dev": [
            "pytest>=7.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "bcm-expert=bcm_expert:main",
        ],
    },
)

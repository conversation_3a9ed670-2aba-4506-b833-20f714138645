"""
Simple ISO 22301 Standards Checker
Validates BCM documents against ISO 22301 requirements with practical, actionable feedback.
"""

from typing import Dict, List, Tuple
from dataclasses import dataclass
from enum import Enum

class ComplianceStatus(Enum):
    FOUND = "✅ Found"
    MISSING = "❌ Missing"
    INCOMPLETE = "⚠️ Incomplete"
    WEAK = "🔶 Weak"

@dataclass
class StandardRequirement:
    """Individual ISO 22301 requirement with practical validation"""
    clause: str
    title: str
    description: str
    what_to_look_for: List[str]  # Specific things to check
    common_problems: List[str]   # Common issues found
    employee_friendly_fix: str   # Simple instruction for employees
    mandatory: bool = True

@dataclass
class ValidationFinding:
    """Simple validation finding"""
    clause: str
    title: str
    status: ComplianceStatus
    found_items: List[str]
    missing_items: List[str]
    problems: List[str]
    simple_fix: str

class ISO22301SimpleChecker:
    """Simple ISO 22301 compliance checker"""
    
    def __init__(self):
        self.requirements = self._initialize_requirements()
    
    def _initialize_requirements(self) -> Dict[str, StandardRequirement]:
        """Initialize simplified ISO 22301 requirements"""
        
        return {
            # Context of Organization
            "4.1": StandardRequirement(
                clause="4.1",
                title="Understanding the organization and its context",
                description="Document internal and external factors affecting business continuity",
                what_to_look_for=[
                    "List of internal factors (staff, processes, systems)",
                    "List of external factors (suppliers, regulations, threats)",
                    "Stakeholder identification",
                    "Business environment analysis"
                ],
                common_problems=[
                    "No clear list of internal/external factors",
                    "Missing stakeholder analysis",
                    "Vague descriptions without specifics"
                ],
                employee_friendly_fix="Create a simple table listing: 1) Internal factors (our people, systems, processes), 2) External factors (suppliers, customers, regulations, threats), 3) Key stakeholders and their needs"
            ),
            
            "4.2": StandardRequirement(
                clause="4.2",
                title="Understanding needs and expectations of interested parties",
                description="Identify stakeholders and their BC requirements",
                what_to_look_for=[
                    "Stakeholder list with names/roles",
                    "Each stakeholder's BC expectations",
                    "Contact information for key stakeholders",
                    "Communication requirements"
                ],
                common_problems=[
                    "Generic stakeholder categories without names",
                    "Missing contact details",
                    "No specific expectations documented",
                    "Unclear communication methods"
                ],
                employee_friendly_fix="Create a stakeholder table with: Name, Role, Contact Details, What they expect from BC, How to communicate with them during incidents"
            ),
            
            "4.3": StandardRequirement(
                clause="4.3",
                title="Determining the scope of the BCMS",
                description="Define what is included/excluded in BC management",
                what_to_look_for=[
                    "Clear scope statement",
                    "List of included business functions",
                    "List of excluded items with reasons",
                    "Geographic boundaries",
                    "Time boundaries (working hours, etc.)"
                ],
                common_problems=[
                    "Vague scope without specifics",
                    "No exclusions listed",
                    "Missing geographic/time boundaries",
                    "No justification for exclusions"
                ],
                employee_friendly_fix="Write a clear statement: 'Our BC plan covers [specific departments/functions/locations] and excludes [specific items with reasons why]'"
            ),
            
            "5.2": StandardRequirement(
                clause="5.2",
                title="Business continuity policy",
                description="Management commitment to business continuity",
                what_to_look_for=[
                    "Signed policy statement",
                    "Management commitment statement",
                    "BC objectives mentioned",
                    "Policy review date",
                    "Distribution/communication plan"
                ],
                common_problems=[
                    "No management signature",
                    "Generic policy without company specifics",
                    "No review date",
                    "Not communicated to staff"
                ],
                employee_friendly_fix="Get management to sign a policy stating: 'We commit to maintaining business continuity, will provide resources, and review this annually on [date]'"
            ),
            
            "6.2": StandardRequirement(
                clause="6.2",
                title="Business continuity objectives",
                description="Specific, measurable BC goals",
                what_to_look_for=[
                    "Specific BC objectives (e.g., 'Resume operations within 4 hours')",
                    "Measurable targets with numbers",
                    "Responsible person for each objective",
                    "Review dates",
                    "Progress monitoring method"
                ],
                common_problems=[
                    "Vague objectives without numbers",
                    "No responsible persons assigned",
                    "No measurement method",
                    "No review schedule"
                ],
                employee_friendly_fix="Write specific goals like: 'Resume critical operations within X hours, Communicate with customers within Y minutes, Restore IT systems within Z hours' - assign each to a person"
            ),
            
            "7.2": StandardRequirement(
                clause="7.2",
                title="Competence",
                description="Staff training and skills for BC",
                what_to_look_for=[
                    "BC training records",
                    "Skills matrix for BC roles",
                    "Training schedule/plan",
                    "Competency assessment results",
                    "Training materials/content"
                ],
                common_problems=[
                    "No training records",
                    "Generic training without role-specific content",
                    "No skills assessment",
                    "Outdated training materials"
                ],
                employee_friendly_fix="Create a training record showing: Who was trained, When, What topics covered, Test results, Next training due date"
            ),
            
            "8.2": StandardRequirement(
                clause="8.2",
                title="Business impact analysis and risk assessment",
                description="Analyze business impacts and risks",
                what_to_look_for=[
                    "List of critical business processes",
                    "Impact analysis with specific timeframes",
                    "Risk assessment with likelihood/impact ratings",
                    "Recovery time objectives (RTO)",
                    "Recovery point objectives (RPO)",
                    "Dependencies mapping"
                ],
                common_problems=[
                    "Generic process list without specifics",
                    "No time-based impact analysis",
                    "Missing RTO/RPO values",
                    "No dependency mapping",
                    "Qualitative only (no numbers)"
                ],
                employee_friendly_fix="For each critical process, document: What it does, Maximum downtime acceptable, What it depends on, Impact if stopped for 1hr/4hrs/1day/1week"
            ),
            
            "8.4": StandardRequirement(
                clause="8.4",
                title="Business continuity plans and procedures",
                description="Detailed response and recovery procedures",
                what_to_look_for=[
                    "Step-by-step response procedures",
                    "Contact lists with current phone numbers",
                    "Alternative work locations",
                    "Recovery procedures for each critical process",
                    "Communication templates",
                    "Decision-making authority"
                ],
                common_problems=[
                    "High-level procedures without specific steps",
                    "Outdated contact information",
                    "No alternative locations identified",
                    "Missing communication templates",
                    "Unclear decision authority"
                ],
                employee_friendly_fix="Write detailed steps: 1) Who to call first, 2) What to say, 3) Where to go, 4) What to do, 5) How to communicate with customers/staff - include current phone numbers and addresses"
            ),
            
            "8.5": StandardRequirement(
                clause="8.5",
                title="Exercising and testing",
                description="Regular testing of BC plans",
                what_to_look_for=[
                    "Test schedule/calendar",
                    "Test results and reports",
                    "Lessons learned documentation",
                    "Plan updates based on tests",
                    "Different types of tests (tabletop, simulation, full)"
                ],
                common_problems=[
                    "No regular testing schedule",
                    "Tests not documented",
                    "No follow-up actions from tests",
                    "Only one type of test conducted"
                ],
                employee_friendly_fix="Schedule quarterly tests: Month 1-Tabletop discussion, Month 2-Partial test, Month 3-Full test, Month 4-Review and update plans. Document what worked and what didn't"
            ),
            
            "9.1": StandardRequirement(
                clause="9.1",
                title="Monitoring and measurement",
                description="Track BC performance",
                what_to_look_for=[
                    "BC performance metrics/KPIs",
                    "Regular monitoring reports",
                    "Measurement methods",
                    "Performance targets",
                    "Trend analysis"
                ],
                common_problems=[
                    "No specific metrics defined",
                    "No regular monitoring",
                    "No performance targets",
                    "No trend tracking"
                ],
                employee_friendly_fix="Define simple metrics like: Test completion rate, Staff training completion, Plan update frequency, Response time achievements - track monthly"
            ),
            
            "9.2": StandardRequirement(
                clause="9.2",
                title="Internal audit",
                description="Regular BC system audits",
                what_to_look_for=[
                    "Audit schedule",
                    "Audit reports",
                    "Audit findings and actions",
                    "Auditor qualifications",
                    "Follow-up on audit actions"
                ],
                common_problems=[
                    "No regular audit schedule",
                    "Audits not documented",
                    "No follow-up on findings",
                    "Unqualified auditors"
                ],
                employee_friendly_fix="Schedule annual BC audits by someone independent, document findings, create action plans with deadlines, track completion"
            ),
            
            "9.3": StandardRequirement(
                clause="9.3",
                title="Management review",
                description="Regular management review of BC system",
                what_to_look_for=[
                    "Management review meetings/minutes",
                    "Review of BC performance",
                    "Decisions and actions from reviews",
                    "Resource allocation decisions",
                    "System improvement decisions"
                ],
                common_problems=[
                    "No regular management reviews",
                    "Reviews not documented",
                    "No decisions or actions recorded",
                    "No resource commitments"
                ],
                employee_friendly_fix="Schedule quarterly management meetings to review: BC performance, test results, training completion, needed resources, plan updates - document decisions"
            ),
            
            "10.1": StandardRequirement(
                clause="10.1",
                title="Nonconformity and corrective action",
                description="Handle problems and improvements",
                what_to_look_for=[
                    "Problem/incident reporting system",
                    "Root cause analysis process",
                    "Corrective action plans",
                    "Action tracking and completion",
                    "Effectiveness verification"
                ],
                common_problems=[
                    "No systematic problem tracking",
                    "No root cause analysis",
                    "Actions not tracked to completion",
                    "No verification of effectiveness"
                ],
                employee_friendly_fix="Create a simple log: Problem description, Root cause, Action needed, Who responsible, Due date, Completion status"
            ),
            
            "10.2": StandardRequirement(
                clause="10.2",
                title="Continual improvement",
                description="Ongoing BC system enhancement",
                what_to_look_for=[
                    "Improvement opportunities identification",
                    "Improvement projects/initiatives",
                    "Performance trend analysis",
                    "Best practice adoption",
                    "Innovation in BC approaches"
                ],
                common_problems=[
                    "No systematic improvement process",
                    "No trend analysis",
                    "Reactive only (no proactive improvement)",
                    "No learning from others"
                ],
                employee_friendly_fix="Regularly ask: What can we do better? What did others do that worked? What trends show we need to improve? Document and implement improvements"
            )
        }
    
    def get_all_requirements(self) -> Dict[str, StandardRequirement]:
        """Get all requirements"""
        return self.requirements
    
    def get_requirement(self, clause: str) -> StandardRequirement:
        """Get specific requirement"""
        return self.requirements.get(clause)
    
    def get_critical_requirements(self) -> List[str]:
        """Get list of critical requirement clauses"""
        return ["4.3", "5.2", "6.2", "8.2", "8.4", "9.2"]
    
    def get_mandatory_documents_checklist(self) -> Dict[str, List[str]]:
        """Get checklist of mandatory documents with what to look for"""
        return {
            "Business Continuity Policy (5.2)": [
                "Signed by senior management",
                "States commitment to BC",
                "Mentions compliance with ISO 22301",
                "Has review date",
                "Communicated to all staff"
            ],
            "BCMS Scope Statement (4.3)": [
                "Lists included business functions",
                "Lists excluded items with reasons",
                "Defines geographic boundaries",
                "Defines time boundaries",
                "Approved by management"
            ],
            "Business Continuity Objectives (6.2)": [
                "Specific, measurable targets",
                "Assigned to responsible persons",
                "Have deadlines",
                "Linked to business needs",
                "Regularly reviewed"
            ],
            "Business Impact Analysis (8.2)": [
                "Lists all critical processes",
                "Shows impact over time (1hr, 4hr, 1day, 1week)",
                "Includes Recovery Time Objectives (RTO)",
                "Includes Recovery Point Objectives (RPO)",
                "Maps dependencies between processes"
            ],
            "Risk Assessment (8.2)": [
                "Lists potential threats/risks",
                "Shows likelihood ratings",
                "Shows impact ratings",
                "Includes risk treatment plans",
                "Regularly updated"
            ],
            "Business Continuity Plans (8.4)": [
                "Step-by-step procedures",
                "Current contact lists",
                "Alternative work locations",
                "Communication templates",
                "Clear roles and responsibilities"
            ],
            "Training Records (7.2)": [
                "Who was trained",
                "When training occurred",
                "What was covered",
                "Test/assessment results",
                "Next training due dates"
            ],
            "Test/Exercise Records (8.5)": [
                "Test schedule/calendar",
                "Test scenarios used",
                "Results and findings",
                "Lessons learned",
                "Plan updates made"
            ],
            "Audit Reports (9.2)": [
                "Audit schedule",
                "Audit findings",
                "Corrective actions",
                "Action completion status",
                "Follow-up audits"
            ],
            "Management Review Records (9.3)": [
                "Meeting minutes/records",
                "Performance review results",
                "Decisions made",
                "Resource commitments",
                "Improvement actions"
            ]
        }

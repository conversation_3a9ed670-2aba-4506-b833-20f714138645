"""
Simple BCM Validator Package
Easy-to-use BCM document validation with practical, actionable feedback.
"""

from .simple_bcm_validator import SimpleBCMValidator, SimpleValidationReport
from .iso_standards_checker import ISO22301Sim<PERSON><PERSON>he<PERSON>, StandardRequirement, ComplianceStatus
from .practical_gap_analyzer import PracticalGapAnalyzer, PracticalGap
from .simple_document_processor import SimpleDocumentProcessor, DocumentInfo

__version__ = "1.0.0"
__author__ = "BCM Expert Team"

__all__ = [
    'SimpleBCMValidator',
    'SimpleValidationReport', 
    'ISO22301SimpleChecker',
    'StandardRequirement',
    'ComplianceStatus',
    'PracticalGapAnalyzer',
    'PracticalGap',
    'SimpleDocumentProcessor',
    'DocumentInfo'
]

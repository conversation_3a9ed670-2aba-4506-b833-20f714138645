"""
Practical Gap Analyzer
Identifies specific, actionable gaps in BCM documents that employees can easily fix.
"""

import re
from typing import Dict, <PERSON>, Tuple, Any
from dataclasses import dataclass

@dataclass
class PracticalGap:
    """A specific, actionable gap that needs fixing"""
    section: str
    problem: str
    impact: str
    simple_fix: str
    urgency: str  # "High", "Medium", "Low"
    example: str

@dataclass
class DetailedFinding:
    """Detailed finding with specific examples"""
    topic: str
    status: str  # "Good", "Needs Improvement", "Missing"
    found_items: List[str]
    missing_items: List[str]
    specific_problems: List[str]
    employee_actions: List[str]

class PracticalGapAnalyzer:
    """Analyzes documents for practical, fixable gaps"""
    
    def __init__(self):
        self.common_patterns = self._initialize_patterns()
    
    def _initialize_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize patterns to look for common problems"""
        
        return {
            "contact_information": {
                "good_patterns": [
                    r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # Phone numbers
                    r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Email
                    r'\b(?:mobile|cell|phone|tel|telephone|email|contact)\b.*?[:]\s*\S+',  # Contact labels
                ],
                "problems_to_find": [
                    "Names without phone numbers",
                    "Roles without contact details",
                    "Outdated contact information",
                    "Generic contact info (e.g., 'IT Department' without specific person)"
                ],
                "what_to_check": "Contact lists, emergency contacts, vendor information, key personnel"
            },
            
            "vendor_information": {
                "good_patterns": [
                    r'vendor|supplier|contractor',
                    r'service\s+level\s+agreement|sla',
                    r'contract|agreement',
                    r'backup\s+supplier|alternative\s+vendor'
                ],
                "problems_to_find": [
                    "Vendor names without contact details",
                    "No backup/alternative vendors listed",
                    "Missing service level agreements",
                    "No vendor criticality assessment"
                ],
                "what_to_check": "Vendor lists, supplier information, critical service providers"
            },
            
            "specific_procedures": {
                "good_patterns": [
                    r'step\s+\d+|first|second|third|then|next|finally',
                    r'procedure|process|instruction',
                    r'who:|what:|when:|where:|how:'
                ],
                "problems_to_find": [
                    "High-level statements without specific steps",
                    "No clear sequence of actions",
                    "Missing who/what/when/where details",
                    "Vague instructions like 'contact relevant parties'"
                ],
                "what_to_check": "Emergency procedures, recovery steps, communication processes"
            },
            
            "time_specifications": {
                "good_patterns": [
                    r'\b\d+\s*(?:minutes?|hours?|days?|weeks?)\b',
                    r'within\s+\d+',
                    r'rto|recovery\s+time\s+objective',
                    r'rpo|recovery\s+point\s+objective'
                ],
                "problems_to_find": [
                    "No specific timeframes mentioned",
                    "Vague terms like 'as soon as possible'",
                    "Missing RTO/RPO values",
                    "No time-based impact analysis"
                ],
                "what_to_check": "Recovery objectives, response times, impact analysis"
            },
            
            "roles_responsibilities": {
                "good_patterns": [
                    r'responsible\s+for|accountable\s+for|role\s+of',
                    r'manager|coordinator|team\s+lead|director',
                    r'authority|decision|approve'
                ],
                "problems_to_find": [
                    "Generic roles without specific names",
                    "Unclear decision-making authority",
                    "No backup/deputy assignments",
                    "Overlapping or conflicting responsibilities"
                ],
                "what_to_check": "Organizational charts, role definitions, decision matrices"
            },
            
            "location_information": {
                "good_patterns": [
                    r'address|location|site|building|floor|room',
                    r'alternative\s+site|backup\s+location|recovery\s+site',
                    r'\d+\s+\w+\s+(?:street|road|avenue|drive|lane)'
                ],
                "problems_to_find": [
                    "No specific addresses for alternative sites",
                    "Missing directions to recovery locations",
                    "No capacity information for alternative sites",
                    "Unclear access procedures for backup locations"
                ],
                "what_to_check": "Alternative work sites, recovery locations, meeting points"
            },
            
            "communication_details": {
                "good_patterns": [
                    r'communication\s+plan|notification|alert|message',
                    r'template|script|announcement',
                    r'media|press|public|customer\s+communication'
                ],
                "problems_to_find": [
                    "No communication templates provided",
                    "Missing stakeholder notification lists",
                    "No social media/public communication plan",
                    "Unclear communication timing and frequency"
                ],
                "what_to_check": "Communication plans, message templates, notification procedures"
            },
            
            "resource_specifications": {
                "good_patterns": [
                    r'equipment|resources|supplies|inventory',
                    r'backup\s+equipment|spare|alternative',
                    r'quantity|amount|number\s+of'
                ],
                "problems_to_find": [
                    "Generic resource lists without quantities",
                    "No backup equipment identified",
                    "Missing resource location information",
                    "No resource procurement procedures"
                ],
                "what_to_check": "Equipment lists, resource inventories, backup supplies"
            }
        }
    
    def analyze_document_content(self, document_text: str, document_sections: List[Any]) -> List[PracticalGap]:
        """Analyze document for practical gaps"""
        gaps = []
        
        # Analyze each pattern category
        for category, pattern_info in self.common_patterns.items():
            category_gaps = self._analyze_category(document_text, document_sections, category, pattern_info)
            gaps.extend(category_gaps)
        
        # Add specific ISO requirement gaps
        iso_gaps = self._analyze_iso_specific_gaps(document_text, document_sections)
        gaps.extend(iso_gaps)
        
        return gaps
    
    def _analyze_category(self, text: str, sections: List[Any], category: str, pattern_info: Dict) -> List[PracticalGap]:
        """Analyze a specific category for gaps"""
        gaps = []
        text_lower = text.lower()
        
        # Check if category is mentioned but poorly implemented
        category_mentioned = any(re.search(pattern, text_lower) for pattern in pattern_info.get("good_patterns", []))
        
        if category == "contact_information":
            gaps.extend(self._check_contact_information(text, sections))
        elif category == "vendor_information":
            gaps.extend(self._check_vendor_information(text, sections))
        elif category == "specific_procedures":
            gaps.extend(self._check_procedure_specificity(text, sections))
        elif category == "time_specifications":
            gaps.extend(self._check_time_specifications(text, sections))
        elif category == "roles_responsibilities":
            gaps.extend(self._check_roles_responsibilities(text, sections))
        elif category == "location_information":
            gaps.extend(self._check_location_information(text, sections))
        elif category == "communication_details":
            gaps.extend(self._check_communication_details(text, sections))
        elif category == "resource_specifications":
            gaps.extend(self._check_resource_specifications(text, sections))
        
        return gaps
    
    def _check_contact_information(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check for contact information gaps"""
        gaps = []
        
        # Look for names without phone numbers
        name_patterns = [
            r'(?:manager|director|coordinator|lead|contact|responsible):\s*([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)(?:\s*-\s*(?:manager|director|coordinator))'
        ]
        
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        names_found = []
        for pattern in name_patterns:
            names_found.extend(re.findall(pattern, text, re.IGNORECASE))
        
        phones_found = len(re.findall(phone_pattern, text))
        emails_found = len(re.findall(email_pattern, text))
        
        if len(names_found) > phones_found + emails_found:
            gaps.append(PracticalGap(
                section="Contact Information",
                problem=f"Found {len(names_found)} names but only {phones_found} phone numbers and {emails_found} email addresses",
                impact="During emergencies, staff won't know how to reach key people",
                simple_fix="Add phone numbers and email addresses for each person mentioned. Format: 'John Smith - Manager - ************ - <EMAIL>'",
                urgency="High",
                example="Instead of 'IT Manager: John Smith', write 'IT Manager: John Smith - ************ - <EMAIL>'"
            ))
        
        return gaps
    
    def _check_vendor_information(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check for vendor information gaps"""
        gaps = []
        
        vendor_mentions = len(re.findall(r'\b(?:vendor|supplier|contractor)\b', text, re.IGNORECASE))
        contact_mentions = len(re.findall(r'\b(?:phone|email|contact|tel)\b', text, re.IGNORECASE))
        
        if vendor_mentions > 0 and contact_mentions < vendor_mentions:
            gaps.append(PracticalGap(
                section="Vendor/Supplier Information",
                problem="Vendors mentioned but missing contact details",
                impact="Cannot reach critical suppliers during disruptions",
                simple_fix="For each vendor, add: Company name, primary contact person, phone number, email, alternative contact, service provided",
                urgency="High",
                example="Instead of 'IT Vendor: TechCorp', write 'IT Vendor: TechCorp - Contact: Mike Johnson - ************ - <EMAIL> - Backup: Sarah Lee - ************'"
            ))
        
        # Check for backup vendors
        backup_vendor_mentions = len(re.findall(r'\b(?:backup|alternative|secondary)\s+(?:vendor|supplier)\b', text, re.IGNORECASE))
        if vendor_mentions > 0 and backup_vendor_mentions == 0:
            gaps.append(PracticalGap(
                section="Vendor/Supplier Information",
                problem="No backup or alternative vendors identified",
                impact="Single point of failure if primary vendor is unavailable",
                simple_fix="For each critical vendor, identify and document at least one backup supplier with their contact details",
                urgency="Medium",
                example="Primary IT Support: TechCorp (************), Backup IT Support: AlternaTech (555-765-4321)"
            ))
        
        return gaps
    
    def _check_procedure_specificity(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check if procedures are specific enough"""
        gaps = []
        
        # Look for vague procedure language
        vague_patterns = [
            r'contact\s+(?:relevant|appropriate|necessary)\s+(?:parties|people|personnel)',
            r'take\s+(?:appropriate|necessary|required)\s+action',
            r'as\s+(?:soon\s+as\s+possible|needed|required)',
            r'follow\s+(?:standard|normal|usual)\s+procedures'
        ]
        
        vague_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in vague_patterns)
        
        if vague_count > 0:
            gaps.append(PracticalGap(
                section="Procedures and Instructions",
                problem=f"Found {vague_count} vague instructions that are not specific enough",
                impact="Staff won't know exactly what to do during emergencies",
                simple_fix="Replace vague terms with specific instructions: Who exactly to contact, what exactly to do, when exactly to do it",
                urgency="High",
                example="Instead of 'contact relevant parties', write 'Call John Smith (IT Manager) at ************, <NAME_EMAIL> using Template A'"
            ))
        
        # Check for step-by-step procedures
        step_patterns = [r'step\s+\d+', r'\d+\.\s+', r'first|second|third|then|next|finally']
        step_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in step_patterns)
        
        procedure_mentions = len(re.findall(r'\b(?:procedure|process|instruction|response)\b', text, re.IGNORECASE))
        
        if procedure_mentions > 3 and step_count < procedure_mentions:
            gaps.append(PracticalGap(
                section="Procedures and Instructions",
                problem="Procedures mentioned but not broken down into clear steps",
                impact="Staff may miss important steps or do things in wrong order",
                simple_fix="Break down each procedure into numbered steps: 1. Do this, 2. Then do this, 3. Finally do this",
                urgency="Medium",
                example="Emergency Response: 1. Ensure personal safety, 2. Call emergency services if needed, 3. Contact Crisis Manager at ************, 4. Implement evacuation if required"
            ))
        
        return gaps
    
    def _check_time_specifications(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check for time specification gaps"""
        gaps = []
        
        time_patterns = [r'\b\d+\s*(?:minutes?|hours?|days?|weeks?)\b', r'within\s+\d+']
        time_mentions = sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in time_patterns)
        
        recovery_mentions = len(re.findall(r'\b(?:recovery|restore|resume|continue)\b', text, re.IGNORECASE))
        
        if recovery_mentions > 0 and time_mentions < recovery_mentions / 2:
            gaps.append(PracticalGap(
                section="Recovery Time Objectives",
                problem="Recovery activities mentioned but no specific timeframes given",
                impact="No clear expectations for how quickly to recover",
                simple_fix="For each critical process, specify: 'Must resume within X hours' or 'Maximum downtime: Y hours'",
                urgency="High",
                example="Instead of 'restore IT systems quickly', write 'restore IT systems within 4 hours' or 'email system: maximum downtime 2 hours'"
            ))
        
        # Check for RTO/RPO
        rto_rpo_mentions = len(re.findall(r'\b(?:rto|rpo|recovery\s+time\s+objective|recovery\s+point\s+objective)\b', text, re.IGNORECASE))
        if recovery_mentions > 0 and rto_rpo_mentions == 0:
            gaps.append(PracticalGap(
                section="Recovery Objectives",
                problem="No Recovery Time Objectives (RTO) or Recovery Point Objectives (RPO) specified",
                impact="No clear targets for recovery speed and data loss limits",
                simple_fix="Define RTO (how long to recover) and RPO (how much data loss acceptable) for each critical system",
                urgency="Medium",
                example="Email System: RTO = 4 hours (must be working within 4 hours), RPO = 1 hour (maximum 1 hour of email loss acceptable)"
            ))
        
        return gaps
    
    def _check_roles_responsibilities(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check for roles and responsibilities gaps"""
        gaps = []
        
        # Look for generic roles
        generic_roles = [
            r'\b(?:it\s+department|hr\s+department|management|staff|team)\b',
            r'\b(?:responsible\s+person|designated\s+individual|appropriate\s+manager)\b'
        ]
        
        specific_roles = [
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\s*-\s*[A-Z]',  # Name - Title
            r'\b(?:manager|director|coordinator):\s*[A-Z][a-z]+\s+[A-Z][a-z]+'  # Title: Name
        ]
        
        generic_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in generic_roles)
        specific_count = sum(len(re.findall(pattern, text)) for pattern in specific_roles)
        
        if generic_count > specific_count:
            gaps.append(PracticalGap(
                section="Roles and Responsibilities",
                problem=f"Found {generic_count} generic role references but only {specific_count} specific person assignments",
                impact="Unclear who exactly is responsible for what during emergencies",
                simple_fix="Replace generic roles with specific names and titles: 'John Smith - IT Manager' instead of 'IT Department'",
                urgency="High",
                example="Instead of 'IT Department will restore systems', write 'John Smith (IT Manager) will restore systems, backup: Sarah Lee (Senior IT Tech)'"
            ))
        
        return gaps
    
    def _check_location_information(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check for location information gaps"""
        gaps = []
        
        location_mentions = len(re.findall(r'\b(?:location|site|building|address|facility)\b', text, re.IGNORECASE))
        address_patterns = [r'\d+\s+\w+\s+(?:street|road|avenue|drive|lane)', r'\b\d{5}\b']  # Street addresses, zip codes
        address_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in address_patterns)
        
        if location_mentions > 0 and address_count == 0:
            gaps.append(PracticalGap(
                section="Location Information",
                problem="Locations mentioned but no specific addresses provided",
                impact="Staff won't know where to go during emergencies",
                simple_fix="Provide complete addresses for all locations: street address, city, state, zip code, plus directions if needed",
                urgency="High",
                example="Instead of 'backup office', write 'Backup Office: 123 Main Street, Anytown, ST 12345 - Enter through rear door, key with Security Guard'"
            ))
        
        return gaps
    
    def _check_communication_details(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check for communication details gaps"""
        gaps = []
        
        comm_mentions = len(re.findall(r'\b(?:communication|notify|inform|announce)\b', text, re.IGNORECASE))
        template_mentions = len(re.findall(r'\b(?:template|script|message|announcement)\b', text, re.IGNORECASE))
        
        if comm_mentions > 0 and template_mentions == 0:
            gaps.append(PracticalGap(
                section="Communication Templates",
                problem="Communication activities mentioned but no templates or scripts provided",
                impact="Inconsistent or unclear messages during emergencies",
                simple_fix="Create templates for common messages: customer notifications, staff updates, media statements, vendor communications",
                urgency="Medium",
                example="Customer Notification Template: 'Dear [Customer], We are experiencing [issue type]. Expected resolution: [time]. We will update you every [frequency]. Contact us at [emergency number].'"
            ))
        
        return gaps
    
    def _check_resource_specifications(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Check for resource specification gaps"""
        gaps = []
        
        resource_mentions = len(re.findall(r'\b(?:equipment|resources|supplies|tools)\b', text, re.IGNORECASE))
        quantity_patterns = [r'\b\d+\s+(?:units?|pieces?|items?)\b', r'\bquantity:\s*\d+\b']
        quantity_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in quantity_patterns)
        
        if resource_mentions > 0 and quantity_count == 0:
            gaps.append(PracticalGap(
                section="Resource Specifications",
                problem="Resources mentioned but no quantities or specifications provided",
                impact="May not have enough resources when needed",
                simple_fix="Specify quantities, locations, and access procedures for all emergency resources",
                urgency="Medium",
                example="Instead of 'backup equipment', write 'Backup Equipment: 5 laptops, 3 phones, 2 printers - stored in Room 201, key with Facilities Manager'"
            ))
        
        return gaps
    
    def _analyze_iso_specific_gaps(self, text: str, sections: List[Any]) -> List[PracticalGap]:
        """Analyze for specific ISO 22301 requirement gaps"""
        gaps = []
        
        # Check for missing policy signature
        policy_mentioned = len(re.findall(r'\b(?:policy|commitment)\b', text, re.IGNORECASE)) > 0
        signature_mentioned = len(re.findall(r'\b(?:signed|signature|approved\s+by)\b', text, re.IGNORECASE)) > 0
        
        if policy_mentioned and not signature_mentioned:
            gaps.append(PracticalGap(
                section="Business Continuity Policy",
                problem="Policy mentioned but no management signature or approval indicated",
                impact="Policy may not have official management backing",
                simple_fix="Get the policy signed by senior management and include signature/approval date in the document",
                urgency="High",
                example="Add: 'Approved by: [CEO Name], Signature: [Signature], Date: [Date]'"
            ))
        
        # Check for missing scope boundaries
        scope_mentioned = len(re.findall(r'\b(?:scope|boundary|include|exclude)\b', text, re.IGNORECASE)) > 0
        exclusion_mentioned = len(re.findall(r'\b(?:exclusion|excluded|not\s+included)\b', text, re.IGNORECASE)) > 0
        
        if scope_mentioned and not exclusion_mentioned:
            gaps.append(PracticalGap(
                section="BCMS Scope",
                problem="Scope mentioned but no exclusions specified",
                impact="Unclear what is NOT covered by the BC plan",
                simple_fix="Clearly list what is excluded from the BC plan and explain why",
                urgency="Medium",
                example="Exclusions: 1) Non-critical administrative functions (low business impact), 2) Third-party managed services (covered by vendor BC plans)"
            ))
        
        return gaps
    
    def generate_simple_report(self, gaps: List[PracticalGap]) -> str:
        """Generate a simple, employee-friendly report"""
        
        if not gaps:
            return """
🎉 GREAT NEWS! 
Your BCM document looks good - no major practical gaps found!

Keep up the good work and remember to:
- Review and update contact information quarterly
- Test your procedures regularly
- Keep all information current and specific
"""
        
        # Sort gaps by urgency
        high_gaps = [g for g in gaps if g.urgency == "High"]
        medium_gaps = [g for g in gaps if g.urgency == "Medium"]
        low_gaps = [g for g in gaps if g.urgency == "Low"]
        
        report = f"""
📋 BCM DOCUMENT IMPROVEMENT CHECKLIST
=====================================

Found {len(gaps)} areas that need improvement to make your BCM plan more effective.

"""
        
        if high_gaps:
            report += f"""
🚨 HIGH PRIORITY - Fix These First ({len(high_gaps)} items)
================================================

"""
            for i, gap in enumerate(high_gaps, 1):
                report += f"""
{i}. {gap.section}
   Problem: {gap.problem}
   Why it matters: {gap.impact}
   What to do: {gap.simple_fix}
   Example: {gap.example}

"""
        
        if medium_gaps:
            report += f"""
⚠️  MEDIUM PRIORITY - Fix These Next ({len(medium_gaps)} items)
==================================================

"""
            for i, gap in enumerate(medium_gaps, 1):
                report += f"""
{i}. {gap.section}
   Problem: {gap.problem}
   What to do: {gap.simple_fix}
   Example: {gap.example}

"""
        
        if low_gaps:
            report += f"""
📝 LOW PRIORITY - Improvements for Later ({len(low_gaps)} items)
=======================================================

"""
            for i, gap in enumerate(low_gaps, 1):
                report += f"""
{i}. {gap.section}
   What to do: {gap.simple_fix}

"""
        
        report += """
💡 QUICK TIPS FOR SUCCESS:
=========================
✅ Start with HIGH priority items - they have the biggest impact
✅ Fix one item at a time - don't try to do everything at once
✅ Ask for help if you're not sure about something
✅ Test your changes by asking "Would this help during a real emergency?"
✅ Update your document regularly as things change

Remember: A good BCM plan is specific, current, and actionable! 🎯
"""
        
        return report

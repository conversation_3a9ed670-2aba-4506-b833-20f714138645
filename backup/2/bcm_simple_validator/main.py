"""
Main application for Simple BCM Validator
Easy-to-use BCM document validation with practical, actionable feedback.
"""

import os
import sys
from datetime import datetime

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from simple_bcm_validator import SimpleBCMValidator

def main():
    """Main application function"""

    print("🏢 Simple BCM Validator - ISO 22301 Compliance Checker")
    print("=" * 60)
    print("This tool checks your BCM document and tells you exactly what to fix!")
    print()

    # Get document path
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        # Look for common BCM document names
        common_names = [
            "BCM Plan - Ptech - 19082024.pdf",
            "bcm_plan.pdf",
            "business_continuity_plan.pdf",
            "bc_plan.pdf"
        ]

        pdf_path = None
        for name in common_names:
            if os.path.exists(name):
                pdf_path = name
                break

        if not pdf_path:
            print("📄 Please specify your BCM document:")
            print("   python main.py your_bcm_document.pdf")
            print()
            print("Or place your document in this folder with one of these names:")
            for name in common_names:
                print(f"   • {name}")
            return

    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return

    print(f"📄 Validating document: {pdf_path}")
    print()

    try:
        # Initialize validator
        validator = SimpleBCMValidator()

        # Validate document
        print("🔍 Starting validation...")
        report = validator.validate_document(pdf_path)

        # Generate reports
        print("\n📊 Generating reports...")

        # Create output directory
        output_dir = "validation_output"
        os.makedirs(output_dir, exist_ok=True)

        # Generate timestamp for filenames
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Generate main report
        main_report_file = f"{output_dir}/bcm_validation_report_{timestamp}.txt"
        main_report = validator.generate_simple_report(report, main_report_file)

        # Generate checklist
        checklist_file = f"{output_dir}/bcm_improvement_checklist_{timestamp}.txt"
        checklist = validator.generate_checklist_report(report, checklist_file)

        # Display summary
        print("\n" + "=" * 60)
        print("📊 VALIDATION SUMMARY")
        print("=" * 60)
        print(f"Document: {report.document_title}")
        print(f"Status: {report.overall_status}")
        print(f"Pages: {report.document_summary['total_pages']}")
        print(f"Word Count: {report.document_summary['word_count']:,}")
        print(f"Sections: {report.document_summary['section_count']}")
        print()

        # Show critical issues
        if report.critical_issues:
            print("🚨 CRITICAL ISSUES FOUND:")
            for i, issue in enumerate(report.critical_issues[:3], 1):
                print(f"   {i}. {issue}")
            if len(report.critical_issues) > 3:
                print(f"   ... and {len(report.critical_issues) - 3} more")
            print()

        # Show quick wins
        if report.quick_wins:
            print("✅ QUICK WINS (Easy fixes):")
            for i, win in enumerate(report.quick_wins[:3], 1):
                print(f"   {i}. {win}")
            if len(report.quick_wins) > 3:
                print(f"   ... and {len(report.quick_wins) - 3} more")
            print()

        # Show ISO compliance
        found_count = len([f for f in report.iso_findings if f.status.value == "✅ Found"])
        total_count = len(report.iso_findings)
        compliance_rate = (found_count / total_count * 100) if total_count > 0 else 0

        print(f"📋 ISO 22301 COMPLIANCE: {found_count}/{total_count} requirements ({compliance_rate:.1f}%)")
        print()

        # Show generated files
        print("📁 GENERATED REPORTS:")
        print(f"   • Detailed Report: {main_report_file}")
        print(f"   • Improvement Checklist: {checklist_file}")
        print()

        # Show next steps
        print("🎯 NEXT STEPS:")
        print("   1. Read the detailed report for complete analysis")
        print("   2. Use the checklist to track your improvements")
        print("   3. Start with critical issues first")
        print("   4. Do the quick wins for immediate improvements")
        print("   5. Re-run this tool after making changes")
        print()

        print("✅ Validation complete! Check the generated reports for detailed guidance.")

    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()

def interactive_mode():
    """Interactive mode for document selection"""

    print("🏢 Simple BCM Validator - Interactive Mode")
    print("=" * 50)

    while True:
        print("\nOptions:")
        print("1. Validate BCM document")
        print("2. Show ISO 22301 requirements")
        print("3. Exit")

        choice = input("\nSelect option (1-3): ").strip()

        if choice == "1":
            pdf_path = input("Enter path to your BCM document (PDF): ").strip()
            if pdf_path and os.path.exists(pdf_path):
                # Temporarily change sys.argv for main function
                original_argv = sys.argv[:]
                sys.argv = [sys.argv[0], pdf_path]
                main()
                sys.argv = original_argv
            else:
                print("❌ File not found or invalid path")

        elif choice == "2":
            show_iso_requirements()

        elif choice == "3":
            print("👋 Thank you for using Simple BCM Validator!")
            break

        else:
            print("❌ Invalid option. Please try again.")

def show_iso_requirements():
    """Show ISO 22301 requirements information"""

    from iso_standards_checker import ISO22301SimpleChecker

    checker = ISO22301SimpleChecker()
    requirements = checker.get_all_requirements()

    print("\n📋 ISO 22301:2019 REQUIREMENTS OVERVIEW")
    print("=" * 50)

    for clause, req in requirements.items():
        print(f"\n{clause}: {req.title}")
        print(f"   What to look for: {', '.join(req.what_to_look_for[:2])}...")
        print(f"   Quick fix: {req.employee_friendly_fix[:80]}...")

    print(f"\nTotal requirements: {len(requirements)}")
    print("Critical requirements:", ", ".join(checker.get_critical_requirements()))

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        main()

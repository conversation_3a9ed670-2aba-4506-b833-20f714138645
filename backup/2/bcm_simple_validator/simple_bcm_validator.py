"""
Simple BCM Validator
Main validator that checks ISO 22301 compliance and generates practical reports.
"""

import os
import re
from typing import Dict, List, <PERSON><PERSON>
from dataclasses import dataclass
from datetime import datetime

from iso_standards_checker import ISO22301<PERSON><PERSON>ple<PERSON>he<PERSON>, StandardRequirement, ComplianceStatus, ValidationFinding
from practical_gap_analyzer import PracticalGapAnaly<PERSON>, PracticalGap
from simple_document_processor import SimpleDocumentProcessor, DocumentInfo

@dataclass
class SimpleValidationReport:
    """Simple validation report"""
    document_title: str
    validation_date: datetime
    overall_status: str
    iso_findings: List[ValidationFinding]
    practical_gaps: List[PracticalGap]
    quick_wins: List[str]
    critical_issues: List[str]
    document_summary: Dict

class SimpleBCMValidator:
    """Simple BCM validator for practical compliance checking"""

    def __init__(self):
        self.iso_checker = ISO22301SimpleChecker()
        self.gap_analyzer = PracticalGapAnalyzer()
        self.doc_processor = SimpleDocumentProcessor()

    def validate_document(self, file_path: str) -> SimpleValidationReport:
        """Validate BCM document and generate practical report"""

        print(f"📄 Processing document: {file_path}")

        # Process document
        doc_info = self.doc_processor.process_pdf(file_path)
        print(f"✅ Document processed: {doc_info.total_pages} pages, {doc_info.word_count:,} words")

        # Check ISO 22301 requirements
        print("🔍 Checking ISO 22301 requirements...")
        iso_findings = self._check_iso_requirements(doc_info)

        # Analyze practical gaps
        print("🔍 Analyzing practical gaps...")
        practical_gaps = self.gap_analyzer.analyze_document_content(doc_info.full_text, doc_info.sections)

        # Generate overall status
        overall_status = self._determine_overall_status(iso_findings, practical_gaps)

        # Identify quick wins and critical issues
        quick_wins = self._identify_quick_wins(practical_gaps)
        critical_issues = self._identify_critical_issues(iso_findings, practical_gaps)

        # Create report
        report = SimpleValidationReport(
            document_title=doc_info.title or "BCM Document",
            validation_date=datetime.now(),
            overall_status=overall_status,
            iso_findings=iso_findings,
            practical_gaps=practical_gaps,
            quick_wins=quick_wins,
            critical_issues=critical_issues,
            document_summary=self.doc_processor.get_document_summary(doc_info)
        )

        print("✅ Validation complete!")
        return report

    def _check_iso_requirements(self, doc_info: DocumentInfo) -> List[ValidationFinding]:
        """Check document against ISO 22301 requirements"""

        findings = []
        requirements = self.iso_checker.get_all_requirements()

        for clause, requirement in requirements.items():
            finding = self._check_single_requirement(doc_info, requirement)
            findings.append(finding)

        return findings

    def _check_single_requirement(self, doc_info: DocumentInfo, requirement: StandardRequirement) -> ValidationFinding:
        """Check a single ISO requirement"""

        found_items = []
        missing_items = []
        problems = []

        # Search for what we're looking for
        for item in requirement.what_to_look_for:
            # Create search keywords from the item
            keywords = self._extract_keywords_from_item(item)

            # Search in document
            matches = self.doc_processor.search_content(doc_info, keywords)

            if matches:
                found_items.append(item)
            else:
                missing_items.append(item)

        # Check for common problems
        for problem in requirement.common_problems:
            problem_keywords = self._extract_keywords_from_item(problem)
            # If we find evidence of the problem, add it
            if self._check_for_problem_indicators(doc_info, problem_keywords):
                problems.append(problem)

        # Determine status
        if len(found_items) == len(requirement.what_to_look_for):
            status = ComplianceStatus.FOUND
        elif len(found_items) > len(requirement.what_to_look_for) / 2:
            status = ComplianceStatus.INCOMPLETE
        elif len(found_items) > 0:
            status = ComplianceStatus.WEAK
        else:
            status = ComplianceStatus.MISSING

        return ValidationFinding(
            clause=requirement.clause,
            title=requirement.title,
            status=status,
            found_items=found_items,
            missing_items=missing_items,
            problems=problems,
            simple_fix=requirement.employee_friendly_fix
        )

    def _extract_keywords_from_item(self, item: str) -> List[str]:
        """Extract keywords from requirement item"""
        # Simple keyword extraction
        import re

        # Remove common words and extract meaningful terms
        words = re.findall(r'\b[a-zA-Z]{3,}\b', item.lower())

        # Remove very common words
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word for word in words if word not in stop_words]

        return keywords[:5]  # Limit to top 5 keywords

    def _check_for_problem_indicators(self, doc_info: DocumentInfo, keywords: List[str]) -> bool:
        """Check if document shows signs of common problems"""

        # Simple check - if we find the problem keywords, there might be an issue
        # This is a simplified implementation
        text_lower = doc_info.full_text.lower()

        # Look for negative indicators
        negative_patterns = [
            r'tbd|to\s+be\s+determined|pending|unknown',
            r'contact\s+(?:relevant|appropriate)\s+(?:parties|people)',
            r'as\s+(?:soon\s+as\s+possible|needed|required)',
            r'see\s+(?:attached|separate)\s+document'
        ]

        for pattern in negative_patterns:
            if re.search(pattern, text_lower):
                return True

        return False

    def _determine_overall_status(self, iso_findings: List[ValidationFinding], practical_gaps: List[PracticalGap]) -> str:
        """Determine overall document status"""

        # Count ISO findings by status
        found_count = len([f for f in iso_findings if f.status == ComplianceStatus.FOUND])
        missing_count = len([f for f in iso_findings if f.status == ComplianceStatus.MISSING])

        # Count practical gaps by urgency
        high_gaps = len([g for g in practical_gaps if g.urgency == "High"])

        total_requirements = len(iso_findings)
        compliance_rate = found_count / total_requirements if total_requirements > 0 else 0

        if compliance_rate >= 0.8 and high_gaps == 0:
            return "🟢 GOOD - Document meets most requirements with minor improvements needed"
        elif compliance_rate >= 0.6 and high_gaps <= 2:
            return "🟡 FAIR - Document covers basics but needs significant improvements"
        elif compliance_rate >= 0.4 or high_gaps <= 5:
            return "🟠 POOR - Document has major gaps that need attention"
        else:
            return "🔴 CRITICAL - Document needs substantial work to meet ISO 22301 requirements"

    def _identify_quick_wins(self, practical_gaps: List[PracticalGap]) -> List[str]:
        """Identify easy fixes that can be done quickly"""

        quick_wins = []

        # Look for gaps that are easy to fix
        easy_fix_keywords = ['add phone number', 'add email', 'add address', 'add date', 'get signature']

        for gap in practical_gaps:
            if any(keyword in gap.simple_fix.lower() for keyword in easy_fix_keywords):
                quick_wins.append(f"{gap.section}: {gap.simple_fix}")

        return quick_wins[:5]  # Limit to top 5

    def _identify_critical_issues(self, iso_findings: List[ValidationFinding], practical_gaps: List[PracticalGap]) -> List[str]:
        """Identify critical issues that need immediate attention"""

        critical_issues = []

        # Critical ISO requirements
        critical_clauses = self.iso_checker.get_critical_requirements()

        for finding in iso_findings:
            if finding.clause in critical_clauses and finding.status == ComplianceStatus.MISSING:
                critical_issues.append(f"Missing: {finding.title}")

        # High urgency practical gaps
        for gap in practical_gaps:
            if gap.urgency == "High":
                critical_issues.append(f"{gap.section}: {gap.problem}")

        return critical_issues

    def generate_simple_report(self, report: SimpleValidationReport, output_file: str = None) -> str:
        """Generate simple, employee-friendly report"""

        report_text = f"""
🏢 BCM DOCUMENT VALIDATION REPORT
================================

Document: {report.document_title}
Validation Date: {report.validation_date.strftime('%B %d, %Y')}
Status: {report.overall_status}

📊 QUICK SUMMARY
===============
• Total Pages: {report.document_summary['total_pages']}
• Word Count: {report.document_summary['word_count']:,}
• Sections Found: {report.document_summary['section_count']}
• ISO Requirements Checked: {len(report.iso_findings)}
• Practical Issues Found: {len(report.practical_gaps)}

"""

        # Critical issues
        if report.critical_issues:
            report_text += f"""
🚨 CRITICAL ISSUES - Fix These First!
===================================
"""
            for i, issue in enumerate(report.critical_issues[:5], 1):
                report_text += f"{i}. {issue}\n"

            if len(report.critical_issues) > 5:
                report_text += f"... and {len(report.critical_issues) - 5} more critical issues\n"

        # Quick wins
        if report.quick_wins:
            report_text += f"""

✅ QUICK WINS - Easy Fixes You Can Do Today!
===========================================
"""
            for i, win in enumerate(report.quick_wins, 1):
                report_text += f"{i}. {win}\n"

        # ISO 22301 Status
        report_text += f"""

📋 ISO 22301 REQUIREMENTS STATUS
===============================
"""

        status_counts = {}
        for finding in report.iso_findings:
            status_counts[finding.status.value] = status_counts.get(finding.status.value, 0) + 1

        for status, count in status_counts.items():
            report_text += f"{status}: {count} requirements\n"

        # Missing requirements
        missing_requirements = [f for f in report.iso_findings if f.status == ComplianceStatus.MISSING]
        if missing_requirements:
            report_text += f"""

❌ MISSING REQUIREMENTS ({len(missing_requirements)} items)
=======================================================
"""
            for req in missing_requirements[:5]:
                report_text += f"• {req.clause}: {req.title}\n"
                report_text += f"  What to do: {req.simple_fix}\n\n"

            if len(missing_requirements) > 5:
                report_text += f"... and {len(missing_requirements) - 5} more missing requirements\n"

        # Practical gaps report
        if report.practical_gaps:
            gap_report = self.gap_analyzer.generate_simple_report(report.practical_gaps)
            report_text += f"\n{gap_report}"

        # Mandatory documents checklist
        report_text += f"""

📄 MANDATORY DOCUMENTS CHECKLIST
===============================
Use this checklist to verify you have all required documents:

"""

        mandatory_docs = self.iso_checker.get_mandatory_documents_checklist()
        for doc_name, checklist in mandatory_docs.items():
            report_text += f"□ {doc_name}\n"
            for item in checklist:
                report_text += f"   □ {item}\n"
            report_text += "\n"

        # Next steps
        report_text += f"""
🎯 NEXT STEPS
============
1. Start with CRITICAL ISSUES - these have the biggest impact
2. Do the QUICK WINS - easy improvements that make a big difference
3. Work through missing ISO requirements one by one
4. Use the mandatory documents checklist to ensure nothing is missed
5. Review and update your document regularly

💡 Remember: A good BCM plan is specific, current, and actionable!

Need help? Contact your BCM coordinator or consultant.
"""

        # Save to file if requested
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📁 Report saved to: {output_file}")

        return report_text

    def generate_checklist_report(self, report: SimpleValidationReport, output_file: str = None) -> str:
        """Generate a simple checklist format report"""

        checklist_text = f"""
📋 BCM DOCUMENT IMPROVEMENT CHECKLIST
====================================

Document: {report.document_title}
Date: {report.validation_date.strftime('%B %d, %Y')}

Instructions: Check off each item as you complete it.

🚨 HIGH PRIORITY (Do These First)
================================
"""

        # High priority items
        high_priority_items = []

        # Critical ISO requirements
        for finding in report.iso_findings:
            if finding.clause in self.iso_checker.get_critical_requirements() and finding.status == ComplianceStatus.MISSING:
                high_priority_items.append(f"□ Add {finding.title.lower()}")

        # High urgency practical gaps
        for gap in report.practical_gaps:
            if gap.urgency == "High":
                high_priority_items.append(f"□ {gap.section}: {gap.simple_fix}")

        for item in high_priority_items[:10]:
            checklist_text += f"{item}\n"

        # Medium priority
        checklist_text += f"""

⚠️  MEDIUM PRIORITY (Do These Next)
==================================
"""

        medium_priority_items = []

        # Incomplete ISO requirements
        for finding in report.iso_findings:
            if finding.status == ComplianceStatus.INCOMPLETE:
                medium_priority_items.append(f"□ Improve {finding.title.lower()}")

        # Medium urgency practical gaps
        for gap in report.practical_gaps:
            if gap.urgency == "Medium":
                medium_priority_items.append(f"□ {gap.section}: {gap.simple_fix}")

        for item in medium_priority_items[:10]:
            checklist_text += f"{item}\n"

        # Document maintenance
        checklist_text += f"""

📅 REGULAR MAINTENANCE (Do These Quarterly)
==========================================
□ Update all contact information (phone numbers, emails)
□ Review and update vendor/supplier contact details
□ Check that all addresses and locations are current
□ Verify that all responsible persons are still in their roles
□ Update any changed procedures or processes
□ Review and update recovery time objectives
□ Test communication templates and procedures
□ Update training records
□ Schedule and conduct BC tests/exercises
□ Review and update risk assessments

✅ COMPLETION TRACKING
=====================
Date Started: ___________
High Priority Completed: _____ / {len(high_priority_items)}
Medium Priority Completed: _____ / {len(medium_priority_items)}
Date Completed: ___________
Next Review Due: ___________

Completed by: _________________________
Reviewed by: __________________________
"""

        # Save to file if requested
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(checklist_text)
            print(f"📁 Checklist saved to: {output_file}")

        return checklist_text

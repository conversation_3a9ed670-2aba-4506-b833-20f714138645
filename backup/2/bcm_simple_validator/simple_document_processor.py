"""
Simple Document Processor
Extracts text and basic structure from BCM documents for validation.
"""

import re
from typing import List, Dict, Tuple
from dataclasses import dataclass

try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from pypdf import PdfReader
    except ImportError:
        print("Warning: Neither PyPDF2 nor pypdf found. PDF processing will not work.")
        PdfReader = None

@dataclass
class SimpleSection:
    """Simple document section"""
    title: str
    content: str
    page_number: int

@dataclass
class DocumentInfo:
    """Basic document information"""
    title: str
    total_pages: int
    word_count: int
    sections: List[SimpleSection]
    full_text: str

class SimpleDocumentProcessor:
    """Simple document processor for BCM validation"""
    
    def __init__(self):
        pass
    
    def process_pdf(self, file_path: str) -> DocumentInfo:
        """Process PDF and extract basic information"""
        
        if PdfReader is None:
            raise ImportError("PDF reader not available. Please install PyPDF2 or pypdf.")
        
        try:
            reader = PdfReader(file_path)
            full_text = ""
            
            # Extract text from all pages
            for page_num, page in enumerate(reader.pages, 1):
                page_text = page.extract_text() or ""
                full_text += f"\n--- PAGE {page_num} ---\n{page_text}"
            
            # Get basic info
            total_pages = len(reader.pages)
            word_count = len(full_text.split())
            
            # Extract title (try from metadata first, then from content)
            title = ""
            if reader.metadata and reader.metadata.get('/Title'):
                title = reader.metadata.get('/Title', '')
            else:
                # Try to find title in first few lines
                first_lines = full_text.split('\n')[:10]
                for line in first_lines:
                    line = line.strip()
                    if len(line) > 10 and len(line) < 100 and not line.startswith('---'):
                        title = line
                        break
            
            # Extract sections
            sections = self._extract_sections(full_text)
            
            return DocumentInfo(
                title=title,
                total_pages=total_pages,
                word_count=word_count,
                sections=sections,
                full_text=full_text
            )
            
        except Exception as e:
            raise Exception(f"Error processing PDF: {e}")
    
    def _extract_sections(self, text: str) -> List[SimpleSection]:
        """Extract sections from document text"""
        sections = []
        lines = text.split('\n')
        current_section = None
        current_content = []
        page_number = 1
        
        # Patterns for identifying headings
        heading_patterns = [
            r'^(\d+\.?\d*\.?\d*)\s+([A-Z][^a-z]*[A-Z].*)',  # Numbered headings
            r'^([A-Z][A-Z\s]+)$',  # ALL CAPS headings
            r'^([A-Z][a-zA-Z\s]+):?\s*$',  # Title case headings
            r'^(CHAPTER|SECTION|PART|APPENDIX)\s+(\d+)',  # Chapter/Section headings
        ]
        
        for line in lines:
            line = line.strip()
            
            # Track page numbers
            if line.startswith('--- PAGE'):
                page_match = re.search(r'PAGE (\d+)', line)
                if page_match:
                    page_number = int(page_match.group(1))
                continue
            
            # Skip empty lines
            if not line:
                continue
            
            # Check if line is a heading
            is_heading = False
            heading_title = line
            
            for pattern in heading_patterns:
                match = re.match(pattern, line)
                if match:
                    is_heading = True
                    if len(match.groups()) > 1:
                        heading_title = match.group(2).strip()
                    else:
                        heading_title = match.group(1).strip()
                    break
            
            # Also check for common BCM section titles
            bcm_section_patterns = [
                r'^(?:business\s+continuity|bc)\s+(?:policy|plan|procedure)',
                r'^(?:risk\s+assessment|business\s+impact\s+analysis|bia)',
                r'^(?:emergency\s+response|incident\s+management)',
                r'^(?:recovery\s+procedures|restoration\s+plan)',
                r'^(?:communication\s+plan|notification\s+procedure)',
                r'^(?:roles\s+and\s+responsibilities|organization)',
                r'^(?:contact\s+information|emergency\s+contacts)',
                r'^(?:vendor|supplier)\s+(?:information|management)',
                r'^(?:testing|exercise)\s+(?:plan|procedure)',
                r'^(?:training|awareness)\s+(?:plan|program)'
            ]
            
            if not is_heading:
                for pattern in bcm_section_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        is_heading = True
                        heading_title = line
                        break
            
            if is_heading:
                # Save previous section
                if current_section and current_content:
                    current_section.content = '\n'.join(current_content)
                    sections.append(current_section)
                
                # Start new section
                current_section = SimpleSection(
                    title=heading_title,
                    content="",
                    page_number=page_number
                )
                current_content = []
            else:
                # Add to current section content
                if current_section:
                    current_content.append(line)
                else:
                    # Create a default section for content before first heading
                    current_section = SimpleSection(
                        title="Introduction",
                        content="",
                        page_number=page_number
                    )
                    current_content = [line]
        
        # Add final section
        if current_section and current_content:
            current_section.content = '\n'.join(current_content)
            sections.append(current_section)
        
        return sections
    
    def search_content(self, doc_info: DocumentInfo, keywords: List[str]) -> Dict[str, List[Tuple[str, str]]]:
        """Search for keywords in document content"""
        results = {}
        
        for keyword in keywords:
            matches = []
            keyword_lower = keyword.lower()
            
            for section in doc_info.sections:
                content_lower = section.content.lower()
                
                # Find all occurrences
                if keyword_lower in content_lower:
                    # Extract context around the keyword
                    sentences = re.split(r'[.!?]+', section.content)
                    for sentence in sentences:
                        if keyword_lower in sentence.lower():
                            matches.append((section.title, sentence.strip()))
            
            if matches:
                results[keyword] = matches
        
        return results
    
    def get_document_summary(self, doc_info: DocumentInfo) -> Dict[str, any]:
        """Get basic document summary"""
        
        # Extract top keywords
        words = re.findall(r'\b[a-zA-Z]{4,}\b', doc_info.full_text.lower())
        
        # Remove common stop words
        stop_words = {
            'this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 
            'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there',
            'business', 'continuity', 'plan', 'management', 'system', 'process',
            'procedure', 'information', 'document', 'section', 'shall', 'should'
        }
        
        filtered_words = [word for word in words if word not in stop_words and len(word) > 3]
        
        # Count word frequency
        word_freq = {}
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top keywords
        top_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
        
        return {
            'title': doc_info.title,
            'total_pages': doc_info.total_pages,
            'word_count': doc_info.word_count,
            'section_count': len(doc_info.sections),
            'sections': [{'title': s.title, 'page': s.page_number} for s in doc_info.sections],
            'top_keywords': [word for word, freq in top_keywords]
        }

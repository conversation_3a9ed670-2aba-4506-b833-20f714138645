# BCM Expert - ISO 22301 Document Validator

A comprehensive Business Continuity Management (BCM) document validation system that validates BCM documents against ISO 22301:2019 standards using advanced AI and natural language processing.

## 🌟 Features

### Core Capabilities
- **Comprehensive ISO 22301 Validation**: Validates documents against all 10 clauses of ISO 22301:2019
- **Document Structure Analysis**: Analyzes document structure, sections, and content organization
- **Compliance Scoring**: Provides detailed compliance scores with weighted requirements
- **Gap Analysis**: Identifies missing requirements and provides actionable recommendations
- **Interactive Q&A**: Chat with an AI expert about your BCM documents
- **Multiple Report Formats**: Generate reports in HTML, Excel, CSV, and text formats

### Advanced Features
- **Intelligent Document Parsing**: Extracts text, metadata, and structure from PDF documents
- **NLP-Powered Analysis**: Uses spaCy and NLTK for advanced text processing
- **Keyword Matching**: Sophisticated keyword and phrase matching for compliance indicators
- **Fuzzy Section Matching**: Intelligent section identification even with varying naming conventions
- **Compliance Visualization**: Charts and graphs showing compliance status
- **Actionable Recommendations**: Prioritized action plans for improving compliance

## 📋 ISO 22301:2019 Coverage

The system validates against all major ISO 22301 clauses:

- **Clause 4**: Context of the Organization
- **Clause 5**: Leadership
- **Clause 6**: Planning
- **Clause 7**: Support
- **Clause 8**: Operation
- **Clause 9**: Performance Evaluation
- **Clause 10**: Improvement

### Mandatory Documents Checked
- List of legal, regulatory and other requirements (4.2.2)
- Scope of the BCMS and explanation of exclusions (4.3)
- Business continuity policy (5.2)
- Business continuity objectives (6.2)
- Competencies of personnel (7.2)
- Business continuity plans and procedures (8.4)
- And 8 more mandatory documents...

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Ollama (for LLM functionality)
- BCM document in PDF format

### Installation

1. **Clone the repository**:
```bash
git clone https://github.com/bcmexpert/bcm-validator.git
cd bcm-validator
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Install spaCy language model**:
```bash
python -m spacy download en_core_web_sm
```

4. **Install Ollama and required models** (optional, for LLM features):
```bash
# Install Ollama from https://ollama.ai
ollama pull llama3.2
ollama pull nomic-embed-text
```

### Basic Usage

1. **Place your BCM document** in the project directory and name it `BCM Plan - Ptech - 19082024.pdf` (or update the path in the code)

2. **Run the application**:
```bash
python test.py
```

3. **Choose from available options**:
   - Advanced ISO 22301 compliance validation
   - Basic LLM-based validation
   - Document structure analysis
   - Interactive BCM chat

### Advanced Usage

For full-featured validation with detailed reports:

```bash
python bcm_expert.py --file "your_bcm_document.pdf" --output "reports"
```

For interactive mode:
```bash
python bcm_expert.py --interactive
```

## 📊 Sample Output

### Compliance Report
```
🏢 BCM Expert - ISO 22301 Document Validator
==================================================

📊 Validation Results:
Overall Compliance Score: 78.5%
Total Requirements Checked: 25
Critical Gaps Found: 3
Recommendations: 8

✅ MODERATE - Reasonable compliance, improvements needed

📁 Detailed reports saved to: validation_reports/
```

### Generated Reports
- **Executive Summary**: High-level compliance overview
- **Detailed Analysis**: Clause-by-clause breakdown
- **Gap Analysis**: Prioritized list of missing requirements
- **Action Plan**: Step-by-step improvement roadmap
- **Compliance Charts**: Visual representation of compliance status

## 🏗️ Architecture

### Core Components

1. **ISO 22301 Standards Database** (`iso_22301_standards.py`)
   - Complete ISO 22301:2019 requirements
   - Validation criteria and keywords
   - Compliance scoring algorithms

2. **Document Analyzer** (`document_analyzer.py`)
   - PDF text extraction and processing
   - Document structure identification
   - NLP-powered content analysis

3. **BCM Validator** (`bcm_validator.py`)
   - Main validation engine
   - Compliance level determination
   - Gap analysis and recommendations

4. **Report Generator** (`validation_report.py`)
   - Multiple output formats
   - Visualization and charts
   - Executive summaries and action plans

5. **BCM Expert** (`bcm_expert.py`)
   - Main application interface
   - Interactive mode
   - Command-line interface

## 📈 Compliance Scoring

The system uses a sophisticated weighted scoring algorithm:

- **Fully Compliant**: 100% score for the requirement
- **Partially Compliant**: 50% score for the requirement
- **Non-Compliant**: 0% score for the requirement
- **Not Applicable**: Excluded from scoring

Requirements are weighted based on their importance:
- Critical clauses (4.3, 5.2, 8.4): Weight 1.0
- Important clauses (6.2, 8.2, 9.2): Weight 0.9
- Standard clauses: Weight 0.7-0.8

## 🔧 Configuration

### Customizing Validation Criteria

You can customize validation criteria in `iso_22301_standards.py`:

```python
# Example: Adding custom keywords for a requirement
requirement.validation_criteria.keywords.extend([
    "custom_keyword",
    "specific_term",
    "organization_specific"
])
```

### Adjusting Compliance Thresholds

Modify compliance thresholds in `bcm_validator.py`:

```python
# Current thresholds
if overall_score >= 80:
    compliance_level = ComplianceLevel.FULLY_COMPLIANT
elif overall_score >= 50:
    compliance_level = ComplianceLevel.PARTIALLY_COMPLIANT
```

## 📝 Example Use Cases

### 1. Pre-Certification Assessment
Validate your BCM documentation before formal ISO 22301 certification audit.

### 2. Internal Audit Support
Use as a tool for internal auditors to assess BCMS compliance.

### 3. Continuous Improvement
Regular validation to ensure ongoing compliance and identify improvement opportunities.

### 4. Training and Education
Help teams understand ISO 22301 requirements through interactive Q&A.

### 5. Consultant Tool
BCM consultants can use this to quickly assess client documentation.

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a virtual environment
3. Install development dependencies:
```bash
pip install -e ".[dev]"
```
4. Run tests:
```bash
pytest
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Wiki](https://github.com/bcmexpert/bcm-validator/wiki)
- **Issues**: [GitHub Issues](https://github.com/bcmexpert/bcm-validator/issues)
- **Discussions**: [GitHub Discussions](https://github.com/bcmexpert/bcm-validator/discussions)

## 🙏 Acknowledgments

- ISO 22301:2019 standard by the International Organization for Standardization
- LangChain for LLM integration capabilities
- spaCy for natural language processing
- The open-source community for various supporting libraries

## 🔮 Roadmap

- [ ] Support for additional document formats (Word, Excel)
- [ ] Integration with popular BCM tools
- [ ] Multi-language support
- [ ] API for integration with other systems
- [ ] Machine learning-based improvement suggestions
- [ ] Real-time collaboration features

---

**Note**: This tool is designed to assist with ISO 22301 compliance assessment but does not replace professional consultation or formal certification processes. Always consult with qualified BCM professionals for critical compliance decisions.

"""
ISO 22301:2019 Business Continuity Management System Standards
This module contains the complete ISO 22301 requirements and validation criteria.
"""

from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

class ComplianceLevel(Enum):
    FULLY_COMPLIANT = "Fully Compliant"
    PARTIALLY_COMPLIANT = "Partially Compliant"
    NON_COMPLIANT = "Non-Compliant"
    NOT_APPLICABLE = "Not Applicable"

@dataclass
class ValidationCriteria:
    """Criteria for validating a specific requirement"""
    keywords: List[str]
    required_sections: List[str]
    validation_questions: List[str]
    weight: float  # Importance weight (0.1 to 1.0)

@dataclass
class ISO22301Requirement:
    """Individual ISO 22301 requirement"""
    clause: str
    title: str
    description: str
    mandatory: bool
    validation_criteria: ValidationCriteria
    examples: List[str]

class ISO22301Standards:
    """Complete ISO 22301:2019 standards database"""

    def __init__(self):
        self.requirements = self._initialize_requirements()
        self.mandatory_documents = self._get_mandatory_documents()

    def _initialize_requirements(self) -> Dict[str, ISO22301Requirement]:
        """Initialize all ISO 22301 requirements"""

        requirements = {
            # Clause 4: Context of the organization
            "4.1": ISO22301Requirement(
                clause="4.1",
                title="Understanding the organization and its context",
                description="Organization must determine external and internal issues relevant to its purpose and that affect its ability to achieve intended outcomes of BCMS",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["context", "internal issues", "external issues", "stakeholders", "environment"],
                    required_sections=["Context Analysis", "Internal Factors", "External Factors"],
                    validation_questions=[
                        "Are internal and external issues identified?",
                        "Is the organizational context clearly defined?",
                        "Are relevant stakeholders identified?"
                    ],
                    weight=0.8
                ),
                examples=["Context analysis document", "Stakeholder analysis", "Environmental scanning"]
            ),

            "4.2": ISO22301Requirement(
                clause="4.2",
                title="Understanding the needs and expectations of interested parties",
                description="Organization must determine interested parties relevant to BCMS and their requirements",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["interested parties", "stakeholders", "requirements", "expectations", "needs"],
                    required_sections=["Stakeholder Analysis", "Requirements Matrix"],
                    validation_questions=[
                        "Are all interested parties identified?",
                        "Are their needs and expectations documented?",
                        "Are legal and regulatory requirements identified?"
                    ],
                    weight=0.9
                ),
                examples=["Stakeholder register", "Requirements matrix", "Legal requirements list"]
            ),

            "4.2.2": ISO22301Requirement(
                clause="4.2.2",
                title="Legal, regulatory and other requirements",
                description="List of legal, regulatory and other requirements applicable to business continuity",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["legal requirements", "regulatory", "compliance", "legislation", "standards"],
                    required_sections=["Legal Requirements", "Regulatory Compliance", "Standards"],
                    validation_questions=[
                        "Is there a comprehensive list of legal requirements?",
                        "Are regulatory requirements identified?",
                        "Is compliance monitoring addressed?"
                    ],
                    weight=1.0
                ),
                examples=["Legal requirements register", "Compliance matrix", "Regulatory updates"]
            ),

            "4.3": ISO22301Requirement(
                clause="4.3",
                title="Determining the scope of the BCMS",
                description="Organization must determine boundaries and applicability of BCMS to establish its scope",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["scope", "boundaries", "applicability", "exclusions", "inclusions"],
                    required_sections=["BCMS Scope", "Boundaries", "Exclusions"],
                    validation_questions=[
                        "Is the BCMS scope clearly defined?",
                        "Are boundaries and exclusions justified?",
                        "Does scope cover all critical business functions?"
                    ],
                    weight=1.0
                ),
                examples=["Scope statement", "Boundary definition", "Exclusion justifications"]
            ),

            # Clause 5: Leadership
            "5.1": ISO22301Requirement(
                clause="5.1",
                title="Leadership and commitment",
                description="Top management must demonstrate leadership and commitment with respect to BCMS",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["leadership", "commitment", "top management", "accountability", "responsibility"],
                    required_sections=["Leadership Statement", "Management Commitment", "Accountability"],
                    validation_questions=[
                        "Is top management commitment demonstrated?",
                        "Are leadership responsibilities defined?",
                        "Is accountability clearly established?"
                    ],
                    weight=0.9
                ),
                examples=["Leadership statement", "Management commitment letter", "Accountability matrix"]
            ),

            "5.2": ISO22301Requirement(
                clause="5.2",
                title="Business continuity policy",
                description="Top management must establish business continuity policy",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["policy", "business continuity", "commitment", "framework", "objectives"],
                    required_sections=["BC Policy", "Policy Statement", "Commitment"],
                    validation_questions=[
                        "Is there a documented BC policy?",
                        "Does policy include commitment to compliance?",
                        "Is policy communicated throughout organization?"
                    ],
                    weight=1.0
                ),
                examples=["Business continuity policy", "Policy communication plan", "Policy review records"]
            ),

            "5.3": ISO22301Requirement(
                clause="5.3",
                title="Organizational roles, responsibilities and authorities",
                description="Top management must ensure responsibilities and authorities for relevant roles are assigned and communicated",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["roles", "responsibilities", "authorities", "assignment", "communication"],
                    required_sections=["Roles and Responsibilities", "Authority Matrix", "Organization Chart"],
                    validation_questions=[
                        "Are BC roles and responsibilities defined?",
                        "Are authorities clearly assigned?",
                        "Is role communication effective?"
                    ],
                    weight=0.8
                ),
                examples=["RACI matrix", "Job descriptions", "Authority delegation"]
            ),

            # Clause 6: Planning
            "6.1": ISO22301Requirement(
                clause="6.1",
                title="Actions to address risks and opportunities",
                description="Organization must plan actions to address risks and opportunities",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["risk assessment", "opportunities", "actions", "planning", "mitigation"],
                    required_sections=["Risk Assessment", "Risk Treatment", "Action Plans"],
                    validation_questions=[
                        "Are risks and opportunities identified?",
                        "Are risk treatment actions planned?",
                        "Is risk monitoring established?"
                    ],
                    weight=0.9
                ),
                examples=["Risk register", "Risk treatment plan", "Risk monitoring procedures"]
            ),

            "6.2": ISO22301Requirement(
                clause="6.2",
                title="Business continuity objectives and planning to achieve them",
                description="Organization must establish business continuity objectives and plan how to achieve them",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["objectives", "targets", "planning", "measurable", "monitoring"],
                    required_sections=["BC Objectives", "Implementation Plans", "Monitoring"],
                    validation_questions=[
                        "Are BC objectives clearly defined?",
                        "Are objectives measurable and achievable?",
                        "Are implementation plans documented?"
                    ],
                    weight=1.0
                ),
                examples=["Objectives statement", "Implementation roadmap", "Performance indicators"]
            ),

            # Clause 7: Support
            "7.1": ISO22301Requirement(
                clause="7.1",
                title="Resources",
                description="Organization must determine and provide resources needed for BCMS",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["resources", "personnel", "infrastructure", "budget", "allocation"],
                    required_sections=["Resource Planning", "Budget", "Infrastructure"],
                    validation_questions=[
                        "Are required resources identified?",
                        "Is resource allocation adequate?",
                        "Is infrastructure suitable for BC?"
                    ],
                    weight=0.7
                ),
                examples=["Resource plan", "Budget allocation", "Infrastructure assessment"]
            ),

            "7.2": ISO22301Requirement(
                clause="7.2",
                title="Competence",
                description="Organization must determine necessary competence of persons and ensure they are competent",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["competence", "training", "skills", "knowledge", "qualifications"],
                    required_sections=["Competency Matrix", "Training Plans", "Skills Assessment"],
                    validation_questions=[
                        "Are competency requirements defined?",
                        "Is training provided where needed?",
                        "Are competencies regularly assessed?"
                    ],
                    weight=1.0
                ),
                examples=["Competency matrix", "Training records", "Skills assessment"]
            ),

            "7.3": ISO22301Requirement(
                clause="7.3",
                title="Awareness",
                description="Persons must be aware of BC policy, their contribution to BCMS effectiveness, and implications of not conforming",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["awareness", "communication", "training", "understanding", "implications"],
                    required_sections=["Awareness Program", "Communication Plan", "Training"],
                    validation_questions=[
                        "Is BC awareness program established?",
                        "Do personnel understand their BC roles?",
                        "Are implications of non-conformance communicated?"
                    ],
                    weight=0.8
                ),
                examples=["Awareness program", "Communication materials", "Training sessions"]
            ),

            "7.4": ISO22301Requirement(
                clause="7.4",
                title="Communication",
                description="Organization must determine internal and external communications relevant to BCMS",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["communication", "internal", "external", "stakeholders", "channels"],
                    required_sections=["Communication Plan", "Stakeholder Communication", "Channels"],
                    validation_questions=[
                        "Is communication plan documented?",
                        "Are communication channels established?",
                        "Is stakeholder communication addressed?"
                    ],
                    weight=0.8
                ),
                examples=["Communication strategy", "Contact lists", "Communication protocols"]
            ),

            "7.5": ISO22301Requirement(
                clause="7.5",
                title="Documented information",
                description="BCMS must include documented information required by standard and determined necessary for effectiveness",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["documentation", "procedures", "records", "control", "management"],
                    required_sections=["Document Control", "Records Management", "Procedures"],
                    validation_questions=[
                        "Is documented information controlled?",
                        "Are procedures documented?",
                        "Is document management effective?"
                    ],
                    weight=0.7
                ),
                examples=["Document control procedure", "Records management", "Document register"]
            ),

            # Clause 8: Operation
            "8.1": ISO22301Requirement(
                clause="8.1",
                title="Operational planning and control",
                description="Organization must plan, implement and control processes needed to meet BCMS requirements",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["operational planning", "processes", "control", "implementation", "monitoring"],
                    required_sections=["Operational Plans", "Process Control", "Implementation"],
                    validation_questions=[
                        "Are operational processes planned?",
                        "Is process control established?",
                        "Are implementation controls effective?"
                    ],
                    weight=0.8
                ),
                examples=["Operational procedures", "Process maps", "Control measures"]
            ),

            "8.2": ISO22301Requirement(
                clause="8.2",
                title="Business impact analysis and risk assessment",
                description="Organization must conduct business impact analysis and risk assessment",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["business impact analysis", "BIA", "risk assessment", "criticality", "dependencies"],
                    required_sections=["BIA", "Risk Assessment", "Impact Analysis", "Dependencies"],
                    validation_questions=[
                        "Is comprehensive BIA conducted?",
                        "Are risks systematically assessed?",
                        "Are business dependencies identified?"
                    ],
                    weight=1.0
                ),
                examples=["BIA report", "Risk assessment", "Dependency mapping"]
            ),

            "8.3": ISO22301Requirement(
                clause="8.3",
                title="Business continuity strategy",
                description="Organization must establish business continuity strategies based on BIA and risk assessment",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["strategy", "continuity", "recovery", "alternatives", "solutions"],
                    required_sections=["BC Strategy", "Recovery Strategies", "Alternative Solutions"],
                    validation_questions=[
                        "Are BC strategies documented?",
                        "Do strategies address identified risks?",
                        "Are recovery options evaluated?"
                    ],
                    weight=1.0
                ),
                examples=["Strategy document", "Recovery options", "Solution alternatives"]
            ),

            "8.4": ISO22301Requirement(
                clause="8.4",
                title="Business continuity plans and procedures",
                description="Organization must establish business continuity plans and procedures",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["plans", "procedures", "response", "recovery", "communication", "coordination"],
                    required_sections=["BC Plans", "Response Procedures", "Recovery Plans", "Communication Plans"],
                    validation_questions=[
                        "Are comprehensive BC plans documented?",
                        "Do plans include response procedures?",
                        "Are communication plans integrated?"
                    ],
                    weight=1.0
                ),
                examples=["Business continuity plan", "Emergency procedures", "Recovery plans"]
            ),

            "8.5": ISO22301Requirement(
                clause="8.5",
                title="Exercising and testing",
                description="Organization must exercise and test business continuity plans and procedures",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["testing", "exercising", "drills", "scenarios", "validation"],
                    required_sections=["Testing Program", "Exercise Plans", "Test Results"],
                    validation_questions=[
                        "Is regular testing program established?",
                        "Are various exercise types conducted?",
                        "Are test results documented and acted upon?"
                    ],
                    weight=0.9
                ),
                examples=["Test schedule", "Exercise reports", "Improvement actions"]
            ),

            # Clause 9: Performance evaluation
            "9.1": ISO22301Requirement(
                clause="9.1",
                title="Monitoring, measurement, analysis and evaluation",
                description="Organization must monitor, measure, analyze and evaluate BCMS performance",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["monitoring", "measurement", "analysis", "evaluation", "performance", "metrics"],
                    required_sections=["Performance Monitoring", "Metrics", "Analysis"],
                    validation_questions=[
                        "Are performance metrics defined?",
                        "Is regular monitoring conducted?",
                        "Are results analyzed and evaluated?"
                    ],
                    weight=1.0
                ),
                examples=["Performance dashboard", "Monitoring reports", "Analysis results"]
            ),

            "9.2": ISO22301Requirement(
                clause="9.2",
                title="Internal audit",
                description="Organization must conduct internal audits to provide information on BCMS conformity and effectiveness",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["internal audit", "audit program", "audit results", "conformity", "effectiveness"],
                    required_sections=["Audit Program", "Audit Reports", "Audit Results"],
                    validation_questions=[
                        "Is internal audit program established?",
                        "Are audits conducted regularly?",
                        "Are audit results documented and acted upon?"
                    ],
                    weight=1.0
                ),
                examples=["Audit program", "Audit reports", "Corrective actions"]
            ),

            "9.3": ISO22301Requirement(
                clause="9.3",
                title="Management review",
                description="Top management must review BCMS at planned intervals",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["management review", "review results", "decisions", "improvements"],
                    required_sections=["Management Review", "Review Results", "Decisions"],
                    validation_questions=[
                        "Are management reviews conducted regularly?",
                        "Are review results documented?",
                        "Are improvement decisions made?"
                    ],
                    weight=1.0
                ),
                examples=["Review minutes", "Management decisions", "Improvement plans"]
            ),

            # Clause 10: Improvement
            "10.1": ISO22301Requirement(
                clause="10.1",
                title="Nonconformity and corrective action",
                description="Organization must react to nonconformity and take corrective action",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["nonconformity", "corrective action", "root cause", "effectiveness"],
                    required_sections=["Nonconformity Management", "Corrective Actions", "Root Cause Analysis"],
                    validation_questions=[
                        "Is nonconformity management process established?",
                        "Are corrective actions taken?",
                        "Is effectiveness of actions verified?"
                    ],
                    weight=1.0
                ),
                examples=["Nonconformity register", "Corrective action plans", "Effectiveness reviews"]
            ),

            "10.2": ISO22301Requirement(
                clause="10.2",
                title="Continual improvement",
                description="Organization must continually improve suitability, adequacy and effectiveness of BCMS",
                mandatory=True,
                validation_criteria=ValidationCriteria(
                    keywords=["continual improvement", "suitability", "adequacy", "effectiveness"],
                    required_sections=["Improvement Program", "Improvement Actions", "Performance Trends"],
                    validation_questions=[
                        "Is continual improvement process established?",
                        "Are improvement opportunities identified?",
                        "Is BCMS effectiveness improving?"
                    ],
                    weight=0.8
                ),
                examples=["Improvement register", "Performance trends", "Enhancement projects"]
            )
        }

        return requirements

    def _get_mandatory_documents(self) -> List[str]:
        """Get list of mandatory documents required by ISO 22301"""
        return [
            "List of legal, regulatory and other requirements (4.2.2)",
            "Scope of the BCMS and explanation of exclusions (4.3)",
            "Business continuity policy (5.2)",
            "Business continuity objectives (6.2)",
            "Competencies of personnel (7.2)",
            "Business continuity plans and procedures (8.4)",
            "Documented communication with interested parties (8.4.3.1)",
            "Records of important information about disruption, actions taken and decisions made (8.4.3.1)",
            "Data and results of monitoring and measurement (9.1.1)",
            "Internal audit program (9.2)",
            "Results of internal audit (9.2)",
            "Results of management review (9.3)",
            "Nature of nonconformities and actions taken (10.1)",
            "Results of corrective actions (10.1)"
        ]

    def get_mandatory_documents(self) -> List[str]:
        """Get list of mandatory documents required by ISO 22301"""
        return self._get_mandatory_documents()

    def get_requirement(self, clause: str) -> ISO22301Requirement:
        """Get specific requirement by clause number"""
        return self.requirements.get(clause)

    def get_all_requirements(self) -> Dict[str, ISO22301Requirement]:
        """Get all requirements"""
        return self.requirements

    def get_mandatory_requirements(self) -> Dict[str, ISO22301Requirement]:
        """Get only mandatory requirements"""
        return {k: v for k, v in self.requirements.items() if v.mandatory}

    def get_requirements_by_clause_group(self, clause_prefix: str) -> Dict[str, ISO22301Requirement]:
        """Get requirements by clause group (e.g., '4', '5', etc.)"""
        return {k: v for k, v in self.requirements.items() if k.startswith(clause_prefix)}

    def get_validation_keywords(self) -> List[str]:
        """Get all validation keywords for document analysis"""
        keywords = set()
        for req in self.requirements.values():
            keywords.update(req.validation_criteria.keywords)
        return list(keywords)

    def calculate_compliance_score(self, compliance_results: Dict[str, ComplianceLevel]) -> float:
        """Calculate overall compliance score based on weighted requirements"""
        total_weight = 0
        weighted_score = 0

        for clause, level in compliance_results.items():
            if clause in self.requirements:
                weight = self.requirements[clause].validation_criteria.weight
                total_weight += weight

                if level == ComplianceLevel.FULLY_COMPLIANT:
                    weighted_score += weight * 1.0
                elif level == ComplianceLevel.PARTIALLY_COMPLIANT:
                    weighted_score += weight * 0.5
                elif level == ComplianceLevel.NON_COMPLIANT:
                    weighted_score += weight * 0.0
                # NOT_APPLICABLE doesn't contribute to score

        return (weighted_score / total_weight * 100) if total_weight > 0 else 0

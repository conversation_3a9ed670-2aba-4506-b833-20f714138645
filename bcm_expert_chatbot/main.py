"""
BCM Expert Chatbot - Main Application
Command-line interface for BCM document validation and Q&A.
"""

import os
import sys
import argparse
from datetime import datetime
from typing import Optional

try:
    from .validation_engine import BCMValidationEngine
    from .chatbot_interface import BCMExpertChatbot
    from .report_generator import BCMReportGenerator
    from .iso_standards import ISO22301Standards
except ImportError:
    from validation_engine import BCMValidationEngine
    from chatbot_interface import BCMExpertChatbot
    from report_generator import BCMReportGenerator
    from iso_standards import ISO22301Standards

def print_banner():
    """Print application banner"""
    print("""
🤖 ===============================================
   BCM EXPERT CHATBOT
   AI-Powered Business Continuity Management
   ISO 22301 Validation & Q&A System
===============================================
""")

def validate_document(file_path: str, output_dir: str = "reports", detailed: bool = True):
    """Validate a BCM document"""
    print(f"🔍 Validating document: {file_path}")

    try:
        # Initialize validation engine
        validator = BCMValidationEngine()

        # Validate document
        result = validator.validate_document(file_path, detailed_analysis=detailed)

        # Print summary
        print(f"\\n✅ Validation completed!")
        print(f"📊 Overall Compliance: {result.overall_compliance:.1%}")
        print(f"⏱️ Processing Time: {result.processing_time:.2f}s")

        # Print status breakdown
        summary = result.summary
        print(f"\\n📋 Status Breakdown:")
        print(f"   ✅ Compliant: {summary['status_counts']['COMPLIANT']}")
        print(f"   ⚠️ Partial: {summary['status_counts']['PARTIAL']}")
        print(f"   ❌ Non-Compliant: {summary['status_counts']['NON_COMPLIANT']}")
        print(f"   ❓ Not Found: {summary['status_counts']['NOT_FOUND']}")

        # Print top gaps
        if summary['critical_gaps']:
            print(f"\\n🚨 Top Critical Gaps:")
            for i, gap in enumerate(summary['critical_gaps'][:5], 1):
                print(f"   {i}. {gap}")

        # Generate reports
        if output_dir:
            generate_reports(result, output_dir)

        return result

    except Exception as e:
        print(f"❌ Error validating document: {e}")
        return None

def generate_reports(validation_result, output_dir: str):
    """Generate validation reports"""
    print(f"\\n📄 Generating reports in: {output_dir}")

    try:
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Initialize report generator
        report_gen = BCMReportGenerator()

        # Generate timestamp for filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = f"bcm_validation_{timestamp}"

        # Generate executive summary
        exec_summary = report_gen.generate_executive_summary(validation_result)
        exec_path = os.path.join(output_dir, f"{base_name}_executive_summary.md")
        report_gen.save_report(exec_summary, exec_path)
        print(f"   ✅ Executive Summary: {exec_path}")

        # Generate detailed report
        detailed_report = report_gen.generate_detailed_report(validation_result)
        detailed_path = os.path.join(output_dir, f"{base_name}_detailed_report.md")
        report_gen.save_report(detailed_report, detailed_path)
        print(f"   ✅ Detailed Report: {detailed_path}")

        # Generate gap analysis
        gap_analysis = report_gen.generate_gap_analysis(validation_result)
        gap_path = os.path.join(output_dir, f"{base_name}_gap_analysis.md")
        report_gen.save_report(gap_analysis, gap_path)
        print(f"   ✅ Gap Analysis: {gap_path}")

        # Export JSON
        json_path = os.path.join(output_dir, f"{base_name}_data.json")
        report_gen.export_json(validation_result, json_path)
        print(f"   ✅ JSON Export: {json_path}")

    except Exception as e:
        print(f"❌ Error generating reports: {e}")

def start_interactive_chat():
    """Start interactive chat mode"""
    print("🤖 Starting BCM Expert Chatbot...")
    print("Type 'quit' or 'exit' to end the session\\n")

    try:
        # Initialize chatbot
        chatbot = BCMExpertChatbot()

        # Create session
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        session = chatbot.create_session(session_id)

        # Print welcome message
        print(session.messages[0].content)
        print("\\n" + "="*60 + "\\n")

        # Chat loop
        while True:
            try:
                # Get user input
                user_input = input("You: ").strip()

                # Check for exit commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("\\n👋 Thank you for using BCM Expert Chatbot!")
                    break

                if not user_input:
                    continue

                # Get chatbot response
                print("\\n🤖 BCM Expert:", end=" ")
                response = chatbot.chat(session_id, user_input)
                print(response)
                print("\\n" + "-"*60 + "\\n")

            except KeyboardInterrupt:
                print("\\n\\n👋 Session ended by user.")
                break
            except Exception as e:
                print(f"\\n❌ Error: {e}")
                print("Please try again.\\n")

    except Exception as e:
        print(f"❌ Error starting chatbot: {e}")

def list_iso_requirements():
    """List ISO 22301 requirements"""
    print("📋 ISO 22301:2019 Requirements\\n")

    try:
        iso_standards = ISO22301Standards()
        requirements_by_category = iso_standards.get_requirements_by_category()

        for category, reqs in requirements_by_category.items():
            print(f"\\n🔹 {category}:")
            for req in reqs:
                print(f"   {req.clause}: {req.title}")

        print(f"\\n📊 Total Requirements: {len(iso_standards.get_all_requirements())}")

    except Exception as e:
        print(f"❌ Error listing requirements: {e}")

def show_requirement_details(clause: str):
    """Show details for a specific requirement"""
    try:
        iso_standards = ISO22301Standards()
        requirement = iso_standards.get_requirement(clause)

        if requirement:
            print(f"\\n📋 ISO 22301 Requirement: {requirement.clause}\\n")
            print(f"Title: {requirement.title}")
            print(f"Description: {requirement.description}")
            print(f"\\nKey Elements:")
            for element in requirement.key_elements:
                print(f"   • {element}")
            print(f"\\nValidation Criteria:")
            for criteria in requirement.validation_criteria:
                print(f"   • {criteria}")
            print(f"\\nCommon Gaps:")
            for gap in requirement.common_gaps:
                print(f"   • {gap}")
        else:
            print(f"❌ Requirement {clause} not found.")

    except Exception as e:
        print(f"❌ Error showing requirement details: {e}")

def main():
    """Main application entry point"""
    print_banner()

    # Simple input handling
    if len(sys.argv) > 1:
        # If file path provided as argument
        file_path = sys.argv[1]
    else:
        # Ask for file path
        print("📄 Please enter the path to your BCM document:")
        file_path = input("File path: ").strip().strip('"').strip("'")

    if not file_path:
        print("❌ No file path provided")
        sys.exit(1)

    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        print("Please check the file path and try again.")
        sys.exit(1)

    # Validate the document
    print(f"\n🔍 Processing BCM document: {file_path}")
    validate_document(file_path, "reports", detailed=True)

if __name__ == "__main__":
    main()

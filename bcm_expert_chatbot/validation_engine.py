"""
BCM Validation Engine
Core validation logic using Ollama models for intelligent BCM document analysis.
"""

import time
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

from .iso_standards import ISO22301Standards, ISORequirement
from .document_processor import BCMDocumentProcessor, ProcessedDocument, DocumentSection

@dataclass
class ValidationFinding:
    """Represents a validation finding for an ISO requirement"""
    iso_clause: str
    iso_title: str
    status: str  # "COMPLIANT", "PARTIAL", "NON_COMPLIANT", "NOT_FOUND"
    confidence: float  # 0.0 to 1.0
    evidence: List[str]  # Text evidence found in document
    gaps: List[str]  # Specific gaps identified
    recommendations: List[str]  # Actionable recommendations
    ai_analysis: str  # Detailed AI analysis
    processing_time: float

@dataclass
class ValidationResult:
    """Complete validation result for a BCM document"""
    document_title: str
    document_path: str
    validation_date: datetime
    overall_compliance: float  # 0.0 to 1.0
    findings: List[ValidationFinding]
    summary: Dict[str, Any]
    processing_time: float

class BCMValidationEngine:
    """Main validation engine using Ollama models"""
    
    def __init__(self, model_name: str = "mistral"):
        self.model_name = model_name
        self.logger = self._setup_logging()
        self.iso_standards = ISO22301Standards()
        self.document_processor = BCMDocumentProcessor()
        
        # Verify Ollama availability
        if not OLLAMA_AVAILABLE:
            raise ImportError("Ollama not available. Install with: pip install ollama")
        
        self._verify_models()
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the validation engine"""
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def _verify_models(self):
        """Verify required Ollama models are available"""
        try:
            models = ollama.list()
            available_models = [model['name'] for model in models['models']]
            
            if self.model_name not in [m.split(':')[0] for m in available_models]:
                self.logger.warning(f"Model {self.model_name} not found. Available models: {available_models}")
            
            if 'nomic-embed-text' not in [m.split(':')[0] for m in available_models]:
                self.logger.warning("nomic-embed-text model not found for embeddings")
                
        except Exception as e:
            self.logger.error(f"Error checking Ollama models: {e}")
    
    def validate_document(self, file_path: str, detailed_analysis: bool = True) -> ValidationResult:
        """Validate BCM document against ISO 22301 standards"""
        start_time = time.time()
        
        self.logger.info(f"🤖 Starting BCM validation for: {file_path}")
        
        # Process document
        self.logger.info("📄 Processing document...")
        document = self.document_processor.process_document(file_path)
        self.logger.info(f"✅ Document processed: {len(document.sections)} sections found")
        
        # Get ISO requirements
        iso_requirements = self.iso_standards.get_all_requirements()
        
        # Validate each requirement
        findings = []
        self.logger.info("🔍 Analyzing ISO 22301 compliance...")
        
        for clause, requirement in iso_requirements.items():
            self.logger.info(f"Checking {clause}: {requirement.title}")
            finding = self._validate_requirement(document, requirement, detailed_analysis)
            findings.append(finding)
        
        # Calculate overall compliance
        overall_compliance = self._calculate_overall_compliance(findings)
        
        # Generate summary
        summary = self._generate_summary(findings)
        
        processing_time = time.time() - start_time
        
        result = ValidationResult(
            document_title=document.title,
            document_path=file_path,
            validation_date=datetime.now(),
            overall_compliance=overall_compliance,
            findings=findings,
            summary=summary,
            processing_time=processing_time
        )
        
        self.logger.info(f"✅ Validation completed in {processing_time:.2f}s")
        self.logger.info(f"📊 Overall compliance: {overall_compliance:.1%}")
        
        return result
    
    def _validate_requirement(self, document: ProcessedDocument, requirement: ISORequirement, detailed: bool) -> ValidationFinding:
        """Validate a specific ISO requirement against the document"""
        start_time = time.time()
        
        # Find relevant sections
        relevant_sections = self._find_relevant_sections(document, requirement)
        
        # Prepare content for AI analysis
        content_for_analysis = self._prepare_content_for_analysis(relevant_sections, requirement)
        
        # Get AI analysis
        ai_analysis = self._get_ai_analysis(content_for_analysis, requirement, detailed)
        
        # Parse AI response
        status, confidence, evidence, gaps, recommendations = self._parse_ai_response(ai_analysis, requirement)
        
        processing_time = time.time() - start_time
        
        return ValidationFinding(
            iso_clause=requirement.clause,
            iso_title=requirement.title,
            status=status,
            confidence=confidence,
            evidence=evidence,
            gaps=gaps,
            recommendations=recommendations,
            ai_analysis=ai_analysis,
            processing_time=processing_time
        )
    
    def _find_relevant_sections(self, document: ProcessedDocument, requirement: ISORequirement) -> List[DocumentSection]:
        """Find document sections relevant to the ISO requirement"""
        relevant_sections = []
        
        # Keywords from requirement
        search_terms = []
        search_terms.extend(requirement.key_elements)
        search_terms.extend([word.lower() for word in requirement.title.split()])
        
        # Search in sections
        for section in document.sections:
            section_text = f"{section.title} {section.content}".lower()
            
            # Check for keyword matches
            matches = sum(1 for term in search_terms if term.lower() in section_text)
            
            if matches > 0:
                section.confidence = min(1.0, matches / len(search_terms))
                relevant_sections.append(section)
        
        # Sort by relevance
        relevant_sections.sort(key=lambda x: x.confidence, reverse=True)
        
        # Return top 5 most relevant sections
        return relevant_sections[:5]
    
    def _prepare_content_for_analysis(self, sections: List[DocumentSection], requirement: ISORequirement) -> str:
        """Prepare content for AI analysis"""
        if not sections:
            return "No relevant content found in the document."
        
        content = f"ISO 22301 Requirement: {requirement.clause} - {requirement.title}\\n\\n"
        content += f"Requirement Description: {requirement.description}\\n\\n"
        content += f"Key Elements Required: {', '.join(requirement.key_elements)}\\n\\n"
        content += "Document Content:\\n"
        
        for i, section in enumerate(sections, 1):
            content += f"\\nSection {i}: {section.title}\\n"
            content += f"{section.content[:1000]}..."  # Limit content length
            content += f"\\n(Relevance: {section.confidence:.2f})\\n"
        
        return content
    
    def _get_ai_analysis(self, content: str, requirement: ISORequirement, detailed: bool) -> str:
        """Get AI analysis using Ollama"""
        
        if detailed:
            prompt = f"""You are an expert ISO 22301 Business Continuity Management auditor. 

Analyze the following document content against the ISO 22301 requirement:

{content}

Please provide a detailed analysis covering:

1. COMPLIANCE STATUS: Is this requirement met? (COMPLIANT/PARTIAL/NON_COMPLIANT/NOT_FOUND)
2. CONFIDENCE LEVEL: How confident are you? (0.0-1.0)
3. EVIDENCE: What specific evidence supports compliance?
4. GAPS: What specific elements are missing or inadequate?
5. RECOMMENDATIONS: Specific, actionable recommendations for improvement

Validation Criteria for this requirement:
{', '.join(requirement.validation_criteria)}

Common gaps to look for:
{', '.join(requirement.common_gaps)}

Provide your analysis in a structured format with clear sections."""

        else:
            prompt = f"""You are an ISO 22301 auditor. Analyze if this document content meets the requirement:

{content}

Requirement: {requirement.title}
Key elements needed: {', '.join(requirement.key_elements)}

Respond with:
STATUS: [COMPLIANT/PARTIAL/NON_COMPLIANT/NOT_FOUND]
CONFIDENCE: [0.0-1.0]
EVIDENCE: [Brief evidence]
GAPS: [Missing elements]
RECOMMENDATIONS: [Key actions needed]"""

        try:
            response = ollama.chat(
                model=self.model_name,
                messages=[{
                    'role': 'user',
                    'content': prompt
                }],
                options={
                    'temperature': 0.1,  # Low temperature for consistent analysis
                    'top_p': 0.9
                }
            )
            
            return response['message']['content']
            
        except Exception as e:
            self.logger.error(f"Error getting AI analysis: {e}")
            return f"Error in AI analysis: {e}"
    
    def _parse_ai_response(self, ai_response: str, requirement: ISORequirement) -> tuple:
        """Parse AI response to extract structured information"""
        
        # Default values
        status = "NOT_FOUND"
        confidence = 0.0
        evidence = []
        gaps = []
        recommendations = []
        
        try:
            response_lower = ai_response.lower()
            
            # Extract status
            if "compliant" in response_lower and "non_compliant" not in response_lower and "non-compliant" not in response_lower:
                if "partial" in response_lower:
                    status = "PARTIAL"
                else:
                    status = "COMPLIANT"
            elif "non_compliant" in response_lower or "non-compliant" in response_lower:
                status = "NON_COMPLIANT"
            elif "partial" in response_lower:
                status = "PARTIAL"
            elif "not_found" in response_lower or "not found" in response_lower:
                status = "NOT_FOUND"
            
            # Extract confidence
            import re
            confidence_match = re.search(r'confidence[:\\s]*([0-9]*\\.?[0-9]+)', response_lower)
            if confidence_match:
                confidence = float(confidence_match.group(1))
                if confidence > 1.0:
                    confidence = confidence / 100.0  # Convert percentage
            else:
                # Estimate confidence based on status
                confidence_map = {
                    "COMPLIANT": 0.8,
                    "PARTIAL": 0.6,
                    "NON_COMPLIANT": 0.7,
                    "NOT_FOUND": 0.9
                }
                confidence = confidence_map.get(status, 0.5)
            
            # Extract evidence
            evidence_section = self._extract_section(ai_response, ["evidence", "supporting evidence"])
            if evidence_section:
                evidence = [line.strip() for line in evidence_section.split('\\n') if line.strip()]
            
            # Extract gaps
            gaps_section = self._extract_section(ai_response, ["gaps", "missing", "deficiencies"])
            if gaps_section:
                gaps = [line.strip() for line in gaps_section.split('\\n') if line.strip()]
            else:
                # Use common gaps from requirement if not found
                if status in ["PARTIAL", "NON_COMPLIANT"]:
                    gaps = requirement.common_gaps[:3]  # Top 3 common gaps
            
            # Extract recommendations
            rec_section = self._extract_section(ai_response, ["recommendations", "actions", "improvements"])
            if rec_section:
                recommendations = [line.strip() for line in rec_section.split('\\n') if line.strip()]
            
        except Exception as e:
            self.logger.warning(f"Error parsing AI response: {e}")
        
        return status, confidence, evidence, gaps, recommendations
    
    def _extract_section(self, text: str, keywords: List[str]) -> Optional[str]:
        """Extract a section from AI response based on keywords"""
        import re
        
        for keyword in keywords:
            pattern = rf'{keyword}[:\\s]*([\\s\\S]*?)(?=\\n\\n|\\n[A-Z]|$)'
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _calculate_overall_compliance(self, findings: List[ValidationFinding]) -> float:
        """Calculate overall compliance score"""
        if not findings:
            return 0.0
        
        # Weight different statuses
        status_weights = {
            "COMPLIANT": 1.0,
            "PARTIAL": 0.5,
            "NON_COMPLIANT": 0.0,
            "NOT_FOUND": 0.0
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for finding in findings:
            weight = finding.confidence
            score = status_weights.get(finding.status, 0.0)
            total_score += score * weight
            total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_summary(self, findings: List[ValidationFinding]) -> Dict[str, Any]:
        """Generate validation summary"""
        status_counts = {
            "COMPLIANT": 0,
            "PARTIAL": 0,
            "NON_COMPLIANT": 0,
            "NOT_FOUND": 0
        }
        
        critical_gaps = []
        top_recommendations = []
        
        for finding in findings:
            status_counts[finding.status] += 1
            
            # Collect critical gaps
            if finding.status in ["NON_COMPLIANT", "PARTIAL"]:
                critical_gaps.extend(finding.gaps[:2])  # Top 2 gaps per finding
            
            # Collect recommendations
            top_recommendations.extend(finding.recommendations[:2])  # Top 2 recommendations per finding
        
        # Remove duplicates and limit
        critical_gaps = list(dict.fromkeys(critical_gaps))[:10]  # Top 10 unique gaps
        top_recommendations = list(dict.fromkeys(top_recommendations))[:10]  # Top 10 unique recommendations
        
        return {
            "status_counts": status_counts,
            "total_requirements": len(findings),
            "critical_gaps": critical_gaps,
            "top_recommendations": top_recommendations,
            "compliance_percentage": {
                "compliant": (status_counts["COMPLIANT"] / len(findings)) * 100,
                "partial": (status_counts["PARTIAL"] / len(findings)) * 100,
                "non_compliant": (status_counts["NON_COMPLIANT"] / len(findings)) * 100,
                "not_found": (status_counts["NOT_FOUND"] / len(findings)) * 100
            }
        }
    
    def quick_validate(self, file_path: str) -> ValidationResult:
        """Quick validation with less detailed analysis"""
        return self.validate_document(file_path, detailed_analysis=False)
    
    def validate_specific_requirements(self, file_path: str, clauses: List[str]) -> ValidationResult:
        """Validate only specific ISO requirements"""
        start_time = time.time()
        
        # Process document
        document = self.document_processor.process_document(file_path)
        
        # Get specific requirements
        iso_requirements = self.iso_standards.get_all_requirements()
        selected_requirements = {clause: req for clause, req in iso_requirements.items() if clause in clauses}
        
        # Validate selected requirements
        findings = []
        for clause, requirement in selected_requirements.items():
            finding = self._validate_requirement(document, requirement, True)
            findings.append(finding)
        
        # Calculate results
        overall_compliance = self._calculate_overall_compliance(findings)
        summary = self._generate_summary(findings)
        processing_time = time.time() - start_time
        
        return ValidationResult(
            document_title=document.title,
            document_path=file_path,
            validation_date=datetime.now(),
            overall_compliance=overall_compliance,
            findings=findings,
            summary=summary,
            processing_time=processing_time
        )

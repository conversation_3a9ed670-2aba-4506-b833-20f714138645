"""
BCM Expert Chatbot Package
AI-powered BCM document validation and Q&A system using Ollama models.
"""

from .validation_engine import BCMValidationEngine, ValidationResult, ValidationFinding
from .document_processor import BCMDocumentProcessor, ProcessedDocument
from .chatbot_interface import BCMExpertChatbot
from .report_generator import BCMReportGenerator
from .iso_standards import ISO22301Standards

__version__ = "1.0.0"
__author__ = "BCM Expert Team"

__all__ = [
    'BCMValidationEngine',
    'ValidationResult',
    'ValidationFinding',
    'BCMDocumentProcessor',
    'ProcessedDocument',
    'BCMExpertChatbot',
    'BCMReportGenerator',
    'ISO22301Standards'
]

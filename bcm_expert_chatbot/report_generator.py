"""
BCM Report Generator
Generate comprehensive validation reports in multiple formats.
"""

import os
import json
from typing import Dict, Any, List
from datetime import datetime
from dataclasses import asdict

from .validation_engine import ValidationResult, ValidationFinding

class BCMReportGenerator:
    """Generate BCM validation reports"""
    
    def __init__(self):
        self.report_templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, str]:
        """Initialize report templates"""
        return {
            "executive_summary": """
# BCM Validation Report - Executive Summary

**Document**: {document_title}
**Validation Date**: {validation_date}
**Overall Compliance**: {overall_compliance:.1%}

## Key Findings

{executive_findings}

## Critical Actions Required

{critical_actions}

## Compliance Overview

{compliance_overview}
""",
            
            "detailed_report": """
# Comprehensive BCM Validation Report

## Document Information
- **Title**: {document_title}
- **File Path**: {document_path}
- **Validation Date**: {validation_date}
- **Processing Time**: {processing_time:.2f} seconds
- **Overall Compliance**: {overall_compliance:.1%}

## Executive Summary

{executive_summary}

## Detailed Findings

{detailed_findings}

## Recommendations

{recommendations}

## Appendix

{appendix}
""",
            
            "gap_analysis": """
# BCM Gap Analysis Report

**Document**: {document_title}
**Analysis Date**: {validation_date}

## Critical Gaps Identified

{critical_gaps}

## Compliance Status by Category

{compliance_by_category}

## Priority Actions

{priority_actions}

## Implementation Roadmap

{implementation_roadmap}
"""
        }
    
    def generate_executive_summary(self, validation_result: ValidationResult) -> str:
        """Generate executive summary report"""
        
        # Prepare executive findings
        executive_findings = self._generate_executive_findings(validation_result)
        
        # Prepare critical actions
        critical_actions = self._generate_critical_actions(validation_result)
        
        # Prepare compliance overview
        compliance_overview = self._generate_compliance_overview(validation_result)
        
        return self.report_templates["executive_summary"].format(
            document_title=validation_result.document_title,
            validation_date=validation_result.validation_date.strftime('%Y-%m-%d %H:%M:%S'),
            overall_compliance=validation_result.overall_compliance,
            executive_findings=executive_findings,
            critical_actions=critical_actions,
            compliance_overview=compliance_overview
        )
    
    def generate_detailed_report(self, validation_result: ValidationResult) -> str:
        """Generate comprehensive detailed report"""
        
        # Prepare sections
        executive_summary = self._generate_executive_summary_section(validation_result)
        detailed_findings = self._generate_detailed_findings_section(validation_result)
        recommendations = self._generate_recommendations_section(validation_result)
        appendix = self._generate_appendix_section(validation_result)
        
        return self.report_templates["detailed_report"].format(
            document_title=validation_result.document_title,
            document_path=validation_result.document_path,
            validation_date=validation_result.validation_date.strftime('%Y-%m-%d %H:%M:%S'),
            processing_time=validation_result.processing_time,
            overall_compliance=validation_result.overall_compliance,
            executive_summary=executive_summary,
            detailed_findings=detailed_findings,
            recommendations=recommendations,
            appendix=appendix
        )
    
    def generate_gap_analysis(self, validation_result: ValidationResult) -> str:
        """Generate gap analysis report"""
        
        # Prepare sections
        critical_gaps = self._generate_critical_gaps_section(validation_result)
        compliance_by_category = self._generate_compliance_by_category_section(validation_result)
        priority_actions = self._generate_priority_actions_section(validation_result)
        implementation_roadmap = self._generate_implementation_roadmap(validation_result)
        
        return self.report_templates["gap_analysis"].format(
            document_title=validation_result.document_title,
            validation_date=validation_result.validation_date.strftime('%Y-%m-%d'),
            critical_gaps=critical_gaps,
            compliance_by_category=compliance_by_category,
            priority_actions=priority_actions,
            implementation_roadmap=implementation_roadmap
        )
    
    def _generate_executive_findings(self, validation_result: ValidationResult) -> str:
        """Generate executive findings summary"""
        findings = []
        
        # Overall compliance
        compliance_level = "High" if validation_result.overall_compliance > 0.8 else "Medium" if validation_result.overall_compliance > 0.5 else "Low"
        findings.append(f"- **Compliance Level**: {compliance_level} ({validation_result.overall_compliance:.1%})")
        
        # Status breakdown
        summary = validation_result.summary
        findings.append(f"- **Compliant Requirements**: {summary['status_counts']['COMPLIANT']}")
        findings.append(f"- **Partially Compliant**: {summary['status_counts']['PARTIAL']}")
        findings.append(f"- **Non-Compliant**: {summary['status_counts']['NON_COMPLIANT']}")
        findings.append(f"- **Not Found**: {summary['status_counts']['NOT_FOUND']}")
        
        # Top gaps
        if summary['critical_gaps']:
            findings.append(f"- **Most Critical Gap**: {summary['critical_gaps'][0]}")
        
        return "\\n".join(findings)
    
    def _generate_critical_actions(self, validation_result: ValidationResult) -> str:
        """Generate critical actions list"""
        actions = []
        
        # Get high-priority findings
        high_priority_findings = [
            f for f in validation_result.findings 
            if f.status in ["NON_COMPLIANT", "NOT_FOUND"] and f.confidence > 0.7
        ]
        
        # Sort by ISO clause
        high_priority_findings.sort(key=lambda x: x.iso_clause)
        
        for i, finding in enumerate(high_priority_findings[:5], 1):
            actions.append(f"{i}. **{finding.iso_clause}**: {finding.iso_title}")
            if finding.recommendations:
                actions.append(f"   - {finding.recommendations[0]}")
        
        return "\\n".join(actions) if actions else "No critical actions identified."
    
    def _generate_compliance_overview(self, validation_result: ValidationResult) -> str:
        """Generate compliance overview"""
        summary = validation_result.summary
        total = summary['total_requirements']
        
        overview = []
        overview.append(f"**Total Requirements Assessed**: {total}")
        overview.append(f"**Compliance Rate**: {summary['compliance_percentage']['compliant']:.1f}%")
        overview.append(f"**Partial Compliance**: {summary['compliance_percentage']['partial']:.1f}%")
        overview.append(f"**Non-Compliance**: {summary['compliance_percentage']['non_compliant']:.1f}%")
        overview.append(f"**Missing Elements**: {summary['compliance_percentage']['not_found']:.1f}%")
        
        return "\\n".join(overview)
    
    def _generate_executive_summary_section(self, validation_result: ValidationResult) -> str:
        """Generate executive summary section for detailed report"""
        summary = f"""
This report presents the results of a comprehensive Business Continuity Management (BCM) 
validation against ISO 22301:2019 standards for the document "{validation_result.document_title}".

**Key Results:**
- Overall compliance score: {validation_result.overall_compliance:.1%}
- {validation_result.summary['status_counts']['COMPLIANT']} requirements fully compliant
- {validation_result.summary['status_counts']['PARTIAL']} requirements partially compliant
- {validation_result.summary['status_counts']['NON_COMPLIANT']} requirements non-compliant
- {validation_result.summary['status_counts']['NOT_FOUND']} requirements not addressed

**Primary Concerns:**
"""
        
        for gap in validation_result.summary['critical_gaps'][:3]:
            summary += f"- {gap}\\n"
        
        return summary
    
    def _generate_detailed_findings_section(self, validation_result: ValidationResult) -> str:
        """Generate detailed findings section"""
        findings_text = ""
        
        # Group findings by status
        by_status = {}
        for finding in validation_result.findings:
            if finding.status not in by_status:
                by_status[finding.status] = []
            by_status[finding.status].append(finding)
        
        # Process each status group
        status_order = ["NON_COMPLIANT", "PARTIAL", "NOT_FOUND", "COMPLIANT"]
        status_titles = {
            "NON_COMPLIANT": "Non-Compliant Requirements",
            "PARTIAL": "Partially Compliant Requirements", 
            "NOT_FOUND": "Missing Requirements",
            "COMPLIANT": "Compliant Requirements"
        }
        
        for status in status_order:
            if status in by_status:
                findings_text += f"\\n### {status_titles[status]}\\n\\n"
                
                for finding in by_status[status]:
                    findings_text += f"#### {finding.iso_clause}: {finding.iso_title}\\n\\n"
                    findings_text += f"**Status**: {finding.status}\\n"
                    findings_text += f"**Confidence**: {finding.confidence:.2f}\\n\\n"
                    
                    if finding.evidence:
                        findings_text += "**Evidence Found:**\\n"
                        for evidence in finding.evidence[:3]:
                            findings_text += f"- {evidence}\\n"
                        findings_text += "\\n"
                    
                    if finding.gaps:
                        findings_text += "**Gaps Identified:**\\n"
                        for gap in finding.gaps[:3]:
                            findings_text += f"- {gap}\\n"
                        findings_text += "\\n"
                    
                    if finding.recommendations:
                        findings_text += "**Recommendations:**\\n"
                        for rec in finding.recommendations[:3]:
                            findings_text += f"- {rec}\\n"
                        findings_text += "\\n"
                    
                    findings_text += "---\\n\\n"
        
        return findings_text
    
    def _generate_recommendations_section(self, validation_result: ValidationResult) -> str:
        """Generate recommendations section"""
        recommendations = "## Priority Recommendations\\n\\n"
        
        # Get top recommendations
        all_recommendations = []
        for finding in validation_result.findings:
            if finding.status in ["NON_COMPLIANT", "PARTIAL"]:
                for rec in finding.recommendations:
                    all_recommendations.append((finding.iso_clause, rec))
        
        # Remove duplicates while preserving order
        seen = set()
        unique_recommendations = []
        for clause, rec in all_recommendations:
            if rec not in seen:
                seen.add(rec)
                unique_recommendations.append((clause, rec))
        
        # Add top 10 recommendations
        for i, (clause, rec) in enumerate(unique_recommendations[:10], 1):
            recommendations += f"{i}. **{clause}**: {rec}\\n\\n"
        
        recommendations += "## Implementation Priority\\n\\n"
        recommendations += """
**High Priority** (Immediate Action Required):
- Non-compliant requirements with high confidence scores
- Missing critical BCM elements (Policy, BIA, Strategy)
- Legal and regulatory compliance gaps

**Medium Priority** (Address within 3 months):
- Partially compliant requirements
- Documentation improvements
- Process enhancements

**Low Priority** (Address within 6 months):
- Minor documentation gaps
- Process optimizations
- Training and awareness improvements
"""
        
        return recommendations
    
    def _generate_appendix_section(self, validation_result: ValidationResult) -> str:
        """Generate appendix section"""
        appendix = "## Validation Methodology\\n\\n"
        appendix += """
This validation was performed using AI-powered analysis against ISO 22301:2019 requirements.
The analysis includes:

1. **Document Processing**: Automated extraction and classification of document content
2. **Requirement Mapping**: Mapping document content to specific ISO 22301 clauses
3. **AI Analysis**: Intelligent assessment of compliance using advanced language models
4. **Gap Identification**: Systematic identification of missing or inadequate elements
5. **Recommendation Generation**: Actionable recommendations for improvement

**Confidence Scoring**: Each finding includes a confidence score (0.0-1.0) indicating the reliability of the assessment.

**Status Definitions**:
- **COMPLIANT**: Requirement is adequately addressed
- **PARTIAL**: Requirement is partially addressed with gaps
- **NON_COMPLIANT**: Requirement is inadequately addressed
- **NOT_FOUND**: No evidence of requirement being addressed
"""
        
        appendix += "\\n## ISO 22301:2019 Requirements Summary\\n\\n"
        
        # Add requirements summary
        from .iso_standards import ISO22301Standards
        iso_standards = ISO22301Standards()
        requirements_by_category = iso_standards.get_requirements_by_category()
        
        for category, reqs in requirements_by_category.items():
            appendix += f"### {category}\\n"
            for req in reqs:
                appendix += f"- **{req.clause}**: {req.title}\\n"
            appendix += "\\n"
        
        return appendix
    
    def _generate_critical_gaps_section(self, validation_result: ValidationResult) -> str:
        """Generate critical gaps section"""
        gaps_text = ""
        
        # Get critical gaps with context
        critical_findings = [
            f for f in validation_result.findings 
            if f.status in ["NON_COMPLIANT", "NOT_FOUND"] and f.confidence > 0.6
        ]
        
        for i, finding in enumerate(critical_findings[:10], 1):
            gaps_text += f"## {i}. {finding.iso_clause}: {finding.iso_title}\\n\\n"
            gaps_text += f"**Status**: {finding.status}\\n"
            gaps_text += f"**Impact**: High - Core BCM requirement\\n\\n"
            
            if finding.gaps:
                gaps_text += "**Specific Gaps:**\\n"
                for gap in finding.gaps:
                    gaps_text += f"- {gap}\\n"
                gaps_text += "\\n"
            
            if finding.recommendations:
                gaps_text += "**Required Actions:**\\n"
                for rec in finding.recommendations:
                    gaps_text += f"- {rec}\\n"
                gaps_text += "\\n"
            
            gaps_text += "---\\n\\n"
        
        return gaps_text
    
    def _generate_compliance_by_category_section(self, validation_result: ValidationResult) -> str:
        """Generate compliance by category section"""
        from .iso_standards import ISO22301Standards
        
        iso_standards = ISO22301Standards()
        requirements_by_category = iso_standards.get_requirements_by_category()
        
        category_text = ""
        
        for category, reqs in requirements_by_category.items():
            category_findings = [
                f for f in validation_result.findings 
                if any(f.iso_clause == req.clause for req in reqs)
            ]
            
            if category_findings:
                compliant = len([f for f in category_findings if f.status == "COMPLIANT"])
                partial = len([f for f in category_findings if f.status == "PARTIAL"])
                non_compliant = len([f for f in category_findings if f.status == "NON_COMPLIANT"])
                not_found = len([f for f in category_findings if f.status == "NOT_FOUND"])
                total = len(category_findings)
                
                compliance_rate = (compliant / total) * 100 if total > 0 else 0
                
                category_text += f"### {category}\\n"
                category_text += f"- **Compliance Rate**: {compliance_rate:.1f}%\\n"
                category_text += f"- **Compliant**: {compliant}/{total}\\n"
                category_text += f"- **Partial**: {partial}/{total}\\n"
                category_text += f"- **Non-Compliant**: {non_compliant}/{total}\\n"
                category_text += f"- **Not Found**: {not_found}/{total}\\n\\n"
        
        return category_text
    
    def _generate_priority_actions_section(self, validation_result: ValidationResult) -> str:
        """Generate priority actions section"""
        actions_text = "## Immediate Actions (0-30 days)\\n\\n"
        
        # High priority actions
        high_priority = [
            f for f in validation_result.findings 
            if f.status == "NON_COMPLIANT" and f.confidence > 0.8
        ]
        
        for i, finding in enumerate(high_priority[:5], 1):
            actions_text += f"{i}. **{finding.iso_clause}**: {finding.iso_title}\\n"
            if finding.recommendations:
                actions_text += f"   Action: {finding.recommendations[0]}\\n"
            actions_text += "\\n"
        
        actions_text += "## Short-term Actions (1-3 months)\\n\\n"
        
        # Medium priority actions
        medium_priority = [
            f for f in validation_result.findings 
            if f.status in ["PARTIAL", "NOT_FOUND"] and f.confidence > 0.7
        ]
        
        for i, finding in enumerate(medium_priority[:5], 1):
            actions_text += f"{i}. **{finding.iso_clause}**: {finding.iso_title}\\n"
            if finding.recommendations:
                actions_text += f"   Action: {finding.recommendations[0]}\\n"
            actions_text += "\\n"
        
        return actions_text
    
    def _generate_implementation_roadmap(self, validation_result: ValidationResult) -> str:
        """Generate implementation roadmap"""
        roadmap = """
## Phase 1: Foundation (Months 1-2)
- Establish BCM Policy and governance structure
- Conduct comprehensive Business Impact Analysis
- Perform detailed risk assessment
- Define BCM scope and objectives

## Phase 2: Strategy Development (Months 2-4)
- Develop business continuity strategies
- Create response and recovery procedures
- Establish communication plans
- Set up resource requirements

## Phase 3: Implementation (Months 4-8)
- Implement BCM procedures
- Conduct training and awareness programs
- Establish monitoring and measurement processes
- Begin testing and exercise program

## Phase 4: Optimization (Months 8-12)
- Conduct internal audits
- Perform management reviews
- Implement continuous improvement processes
- Achieve full ISO 22301 compliance

## Ongoing Activities
- Regular testing and exercises
- Continuous monitoring and measurement
- Annual management reviews
- Continuous improvement initiatives
"""
        return roadmap
    
    def save_report(self, report_content: str, file_path: str, format_type: str = "markdown") -> bool:
        """Save report to file"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            return True
            
        except Exception as e:
            print(f"Error saving report: {e}")
            return False
    
    def export_json(self, validation_result: ValidationResult, file_path: str) -> bool:
        """Export validation result as JSON"""
        try:
            # Convert to dictionary
            result_dict = asdict(validation_result)
            
            # Handle datetime serialization
            result_dict['validation_date'] = validation_result.validation_date.isoformat()
            
            # Save JSON
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Error exporting JSON: {e}")
            return False

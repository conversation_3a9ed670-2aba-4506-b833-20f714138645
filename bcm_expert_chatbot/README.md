# 🤖 BCM Expert Chatbot

**AI-Powered Business Continuity Management Validation & Q&A System**

An intelligent chatbot that validates BCM documents against ISO 22301:2019 standards and provides expert guidance on business continuity management.

## 🎯 Features

### 📋 Document Validation
- **Complete ISO 22301 Compliance Check**: Validates against all 22 main requirements
- **Intelligent Gap Analysis**: AI-powered identification of missing elements
- **Granular Feedback**: Specific, actionable recommendations for each requirement
- **Multiple Document Formats**: Supports PDF, DOCX, and TXT files

### 🤖 Expert Q&A
- **Interactive Chatbot**: Ask questions about BCM and ISO 22301
- **Document-Specific Queries**: Load documents and ask specific questions
- **Contextual Responses**: AI understands your BCM context and provides relevant answers

### 📊 Comprehensive Reporting
- **Executive Summary**: High-level compliance overview
- **Detailed Analysis**: In-depth findings for each requirement
- **Gap Analysis**: Priority-based improvement roadmap
- **Multiple Formats**: Markdown reports and JSON exports

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Ollama installed and running
- Required Ollama models: `mistral` and `nomic-embed-text`

### Installation

1. **Install Ollama models** (if not already installed):
```bash
ollama pull mistral
ollama pull nomic-embed-text
```

2. **Install Python dependencies**:
```bash
pip install -r requirements.txt
```

3. **Verify installation**:
```bash
python -m bcm_expert_chatbot.main requirements
```

## 📖 Usage

### Command Line Interface

#### 1. Validate BCM Document
```bash
# Full validation with detailed reports
python -m bcm_expert_chatbot.main validate "path/to/bcm_document.pdf"

# Quick validation
python -m bcm_expert_chatbot.main validate "document.pdf" --quick

# Custom output directory
python -m bcm_expert_chatbot.main validate "document.pdf" --output "my_reports"
```

#### 2. Interactive Chat
```bash
python -m bcm_expert_chatbot.main chat
```

#### 3. List ISO Requirements
```bash
python -m bcm_expert_chatbot.main requirements
```

#### 4. Get Requirement Details
```bash
python -m bcm_expert_chatbot.main requirement 8.2
```

### Interactive Chat Commands

Once in chat mode, you can use these commands:

- `validate <file_path>` - Validate a BCM document
- `load <file_path>` - Load document for Q&A
- `requirements` - List all ISO 22301 requirements
- `status` - Show validation summary (after validation)
- `help` - Show available commands

### Example Chat Session

```
🤖 BCM Expert: Welcome to BCM Expert Chatbot!

You: validate ./Perpetuuiti_BCM_Plan.pdf

🤖 BCM Expert: ✅ Document Validation Complete!
📄 Document: Perpetuuiti BCM Plan
📊 Overall Compliance: 67.3%
⏱️ Processing Time: 45.2s

Compliance Breakdown:
- ✅ Compliant: 8 requirements
- ⚠️ Partial: 6 requirements  
- ❌ Non-Compliant: 5 requirements
- ❓ Not Found: 3 requirements

You: What's missing in my risk assessment section?

🤖 BCM Expert: Based on your document analysis, your risk assessment section (ISO 22301 clause 8.2) shows several gaps:

1. **Missing Risk Criteria**: No clear definition of risk acceptance criteria
2. **Incomplete Threat Analysis**: Limited coverage of external threats
3. **No Risk Treatment Plans**: Missing specific risk mitigation strategies
...
```

## 🏗️ System Architecture

```
bcm_expert_chatbot/
├── __init__.py              # Package initialization
├── iso_standards.py         # Complete ISO 22301:2019 requirements
├── document_processor.py    # Document parsing and analysis
├── validation_engine.py     # Core validation logic using AI
├── chatbot_interface.py     # Interactive Q&A system
├── report_generator.py      # Report generation in multiple formats
├── main.py                  # Command-line interface
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## 🔧 Technical Details

### AI Models Used
- **Mistral 7B**: Primary analysis and reasoning model
- **Nomic Embed Text**: Document embeddings for semantic search

### Document Processing
- **PDF**: PyPDF2 for text extraction
- **DOCX**: python-docx for Word documents
- **TXT**: Native Python text processing

### Validation Process
1. **Document Analysis**: Extract and classify document sections
2. **Requirement Mapping**: Map content to ISO 22301 requirements
3. **AI Assessment**: Intelligent compliance evaluation
4. **Gap Identification**: Systematic gap analysis
5. **Report Generation**: Comprehensive reporting

## 📊 ISO 22301:2019 Coverage

The system validates against all major ISO 22301 requirements:

### Context (Clauses 4.1-4.4)
- Understanding organization and context
- Interested parties requirements
- BCMS scope determination
- BCMS establishment

### Leadership (Clauses 5.1-5.3)
- Leadership and commitment
- BC policy
- Organizational roles and responsibilities

### Planning (Clauses 6.1-6.2)
- Risk and opportunity actions
- BC objectives and planning

### Support (Clauses 7.1-7.5)
- Resources, competence, awareness
- Communication and documentation

### Operation (Clauses 8.1-8.5)
- Operational planning
- Business impact analysis
- Risk assessment
- BC strategy and procedures
- Exercise programme

### Performance Evaluation (Clauses 9.1-9.3)
- Monitoring and measurement
- Internal audit
- Management review

### Improvement (Clauses 10.1-10.2)
- Nonconformity and corrective action
- Continual improvement

## 📈 Sample Validation Results

```
📊 Overall Compliance: 73.2%

Status Breakdown:
✅ Compliant: 12 requirements (54.5%)
⚠️ Partial: 6 requirements (27.3%)
❌ Non-Compliant: 3 requirements (13.6%)
❓ Not Found: 1 requirement (4.5%)

Top Critical Gaps:
1. Missing business impact analysis methodology
2. Incomplete risk assessment documentation
3. No testing and exercise program
4. Undefined recovery time objectives
5. Missing vendor management procedures
```

## 🔍 Example Questions You Can Ask

### General BCM Questions
- "What is required for business impact analysis?"
- "How do I implement ISO 22301 clause 8.2?"
- "What are the key elements of a BCM policy?"
- "Tell me about testing and exercises in BCM"

### Document-Specific Questions (after loading a document)
- "What's missing in my risk assessment section?"
- "Does my BCM policy meet ISO requirements?"
- "How can I improve my business impact analysis?"
- "What testing procedures should I add?"

### Compliance Questions
- "Which ISO requirements am I not meeting?"
- "What are the most critical gaps in my BCM plan?"
- "How do I achieve full ISO 22301 compliance?"
- "What should be my implementation priority?"

## 🚨 Troubleshooting

### Common Issues

1. **Ollama not running**
   ```bash
   # Start Ollama service
   ollama serve
   ```

2. **Models not found**
   ```bash
   # Install required models
   ollama pull mistral
   ollama pull nomic-embed-text
   ```

3. **Document processing errors**
   - Ensure file path is correct
   - Check file permissions
   - Verify file format (PDF, DOCX, TXT)

4. **Memory issues with large documents**
   - Use `--quick` flag for faster processing
   - Split large documents into smaller sections

### Performance Tips

- **For faster validation**: Use `--quick` flag
- **For better accuracy**: Use full validation mode
- **For large documents**: Process in sections
- **For repeated use**: Keep Ollama service running

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for:
- Code style and standards
- Testing requirements
- Documentation updates
- Feature requests

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review the example usage patterns
- Ensure all prerequisites are installed
- Verify Ollama models are available

---

**Built with ❤️ for the BCM community**

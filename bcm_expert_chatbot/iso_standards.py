"""
ISO 22301:2019 Business Continuity Management Standards
Complete requirements mapping for BCM validation.
"""

from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class ISORequirement:
    """Represents an ISO 22301 requirement"""
    clause: str
    title: str
    description: str
    key_elements: List[str]
    validation_criteria: List[str]
    common_gaps: List[str]

class ISO22301Standards:
    """Complete ISO 22301:2019 standards for BCM validation"""
    
    def __init__(self):
        self.requirements = self._initialize_requirements()
    
    def _initialize_requirements(self) -> Dict[str, ISORequirement]:
        """Initialize complete ISO 22301:2019 requirements"""
        
        requirements = {
            "4.1": ISORequirement(
                clause="4.1",
                title="Understanding the organization and its context",
                description="Organization must understand internal and external issues relevant to BCM",
                key_elements=[
                    "Internal and external context analysis",
                    "Stakeholder identification",
                    "Business environment assessment",
                    "Risk landscape understanding"
                ],
                validation_criteria=[
                    "Context analysis documented",
                    "Internal factors identified",
                    "External factors identified", 
                    "Stakeholder mapping present"
                ],
                common_gaps=[
                    "Missing context analysis",
                    "Incomplete stakeholder identification",
                    "No external factor assessment"
                ]
            ),
            
            "4.2": ISORequirement(
                clause="4.2",
                title="Understanding the needs and expectations of interested parties",
                description="Identify interested parties and their requirements for BCM",
                key_elements=[
                    "Interested parties identification",
                    "Requirements determination",
                    "Legal and regulatory requirements",
                    "Contractual obligations"
                ],
                validation_criteria=[
                    "Interested parties listed",
                    "Requirements documented",
                    "Legal requirements identified",
                    "Regular review process"
                ],
                common_gaps=[
                    "Incomplete stakeholder list",
                    "Missing legal requirements",
                    "No requirement review process"
                ]
            ),
            
            "4.3": ISORequirement(
                clause="4.3",
                title="Determining the scope of the business continuity management system",
                description="Define boundaries and applicability of the BCMS",
                key_elements=[
                    "BCMS scope definition",
                    "Boundary determination",
                    "Applicable requirements",
                    "Scope documentation"
                ],
                validation_criteria=[
                    "Scope clearly defined",
                    "Boundaries documented",
                    "Exclusions justified",
                    "Scope availability"
                ],
                common_gaps=[
                    "Unclear scope definition",
                    "Missing boundary documentation",
                    "Unjustified exclusions"
                ]
            ),
            
            "4.4": ISORequirement(
                clause="4.4",
                title="Business continuity management system",
                description="Establish, implement, maintain and continually improve BCMS",
                key_elements=[
                    "BCMS establishment",
                    "Process implementation",
                    "System maintenance",
                    "Continuous improvement"
                ],
                validation_criteria=[
                    "BCMS processes defined",
                    "Implementation evidence",
                    "Maintenance procedures",
                    "Improvement mechanisms"
                ],
                common_gaps=[
                    "Undefined BCMS processes",
                    "No implementation evidence",
                    "Missing maintenance procedures"
                ]
            ),
            
            "5.1": ISORequirement(
                clause="5.1",
                title="Leadership and commitment",
                description="Top management must demonstrate leadership and commitment to BCMS",
                key_elements=[
                    "Leadership demonstration",
                    "Policy establishment",
                    "Resource provision",
                    "Management commitment"
                ],
                validation_criteria=[
                    "Leadership evidence documented",
                    "Policy approved by management",
                    "Resources allocated",
                    "Commitment statements"
                ],
                common_gaps=[
                    "No leadership evidence",
                    "Missing management commitment",
                    "Insufficient resource allocation"
                ]
            ),
            
            "5.2": ISORequirement(
                clause="5.2",
                title="Policy",
                description="Establish business continuity policy",
                key_elements=[
                    "BC policy establishment",
                    "Policy content requirements",
                    "Policy communication",
                    "Policy availability"
                ],
                validation_criteria=[
                    "Policy document exists",
                    "Required content present",
                    "Communication evidence",
                    "Accessibility confirmed"
                ],
                common_gaps=[
                    "Missing BC policy",
                    "Incomplete policy content",
                    "Poor policy communication"
                ]
            ),
            
            "5.3": ISORequirement(
                clause="5.3",
                title="Organizational roles, responsibilities and authorities",
                description="Define and communicate roles and responsibilities for BCM",
                key_elements=[
                    "Role definition",
                    "Responsibility assignment",
                    "Authority delegation",
                    "Communication of roles"
                ],
                validation_criteria=[
                    "Roles clearly defined",
                    "Responsibilities documented",
                    "Authority levels specified",
                    "Role communication evidence"
                ],
                common_gaps=[
                    "Unclear role definitions",
                    "Missing responsibility matrix",
                    "Undefined authority levels"
                ]
            ),
            
            "6.1": ISORequirement(
                clause="6.1",
                title="Actions to address risks and opportunities",
                description="Determine risks and opportunities for BCMS effectiveness",
                key_elements=[
                    "Risk identification",
                    "Opportunity identification",
                    "Action planning",
                    "Integration into BCMS"
                ],
                validation_criteria=[
                    "Risks documented",
                    "Opportunities identified",
                    "Action plans present",
                    "BCMS integration evidence"
                ],
                common_gaps=[
                    "Incomplete risk identification",
                    "Missing opportunity assessment",
                    "No action plans"
                ]
            ),
            
            "6.2": ISORequirement(
                clause="6.2",
                title="Business continuity objectives and planning to achieve them",
                description="Establish BC objectives and plans to achieve them",
                key_elements=[
                    "BC objectives setting",
                    "Objective characteristics",
                    "Planning for achievement",
                    "Resource determination"
                ],
                validation_criteria=[
                    "Objectives documented",
                    "SMART objectives",
                    "Achievement plans",
                    "Resource allocation"
                ],
                common_gaps=[
                    "Missing BC objectives",
                    "Non-SMART objectives",
                    "No achievement planning"
                ]
            ),
            
            "7.1": ISORequirement(
                clause="7.1",
                title="Resources",
                description="Determine and provide resources needed for BCMS",
                key_elements=[
                    "Resource determination",
                    "Resource provision",
                    "Resource types",
                    "Resource adequacy"
                ],
                validation_criteria=[
                    "Resources identified",
                    "Provision evidence",
                    "All types covered",
                    "Adequacy assessment"
                ],
                common_gaps=[
                    "Insufficient resources",
                    "Missing resource planning",
                    "No adequacy assessment"
                ]
            ),
            
            "7.2": ISORequirement(
                clause="7.2",
                title="Competence",
                description="Ensure competence of persons affecting BCMS performance",
                key_elements=[
                    "Competence determination",
                    "Competence assurance",
                    "Training provision",
                    "Competence evaluation"
                ],
                validation_criteria=[
                    "Competence requirements defined",
                    "Assurance mechanisms",
                    "Training programs",
                    "Evaluation processes"
                ],
                common_gaps=[
                    "Undefined competence requirements",
                    "Missing training programs",
                    "No competence evaluation"
                ]
            ),
            
            "7.3": ISORequirement(
                clause="7.3",
                title="Awareness",
                description="Ensure awareness of BC policy, objectives and roles",
                key_elements=[
                    "Awareness requirements",
                    "Awareness content",
                    "Awareness methods",
                    "Awareness verification"
                ],
                validation_criteria=[
                    "Awareness program exists",
                    "Content covers requirements",
                    "Multiple methods used",
                    "Verification evidence"
                ],
                common_gaps=[
                    "No awareness program",
                    "Limited awareness content",
                    "Single communication method"
                ]
            ),
            
            "7.4": ISORequirement(
                clause="7.4",
                title="Communication",
                description="Determine internal and external communications for BCMS",
                key_elements=[
                    "Communication determination",
                    "Communication content",
                    "Communication timing",
                    "Communication methods"
                ],
                validation_criteria=[
                    "Communication plan exists",
                    "Content specified",
                    "Timing defined",
                    "Methods documented"
                ],
                common_gaps=[
                    "Missing communication plan",
                    "Unclear communication content",
                    "No timing specifications"
                ]
            ),
            
            "7.5": ISORequirement(
                clause="7.5",
                title="Documented information",
                description="Include documented information required by standard and organization",
                key_elements=[
                    "Required documentation",
                    "Document control",
                    "Document availability",
                    "Document protection"
                ],
                validation_criteria=[
                    "All required documents present",
                    "Control procedures exist",
                    "Availability ensured",
                    "Protection measures"
                ],
                common_gaps=[
                    "Missing required documents",
                    "Poor document control",
                    "Limited document availability"
                ]
            ),
            
            "8.1": ISORequirement(
                clause="8.1",
                title="Operational planning and control",
                description="Plan, implement and control processes needed for BCMS",
                key_elements=[
                    "Process planning",
                    "Process implementation",
                    "Process control",
                    "Change control"
                ],
                validation_criteria=[
                    "Planning documentation",
                    "Implementation evidence",
                    "Control mechanisms",
                    "Change procedures"
                ],
                common_gaps=[
                    "Poor process planning",
                    "No implementation evidence",
                    "Missing control mechanisms"
                ]
            ),
            
            "8.2": ISORequirement(
                clause="8.2",
                title="Business impact analysis and risk assessment",
                description="Conduct BIA and risk assessment for business continuity",
                key_elements=[
                    "Business impact analysis",
                    "Risk assessment",
                    "Impact criteria",
                    "Risk criteria"
                ],
                validation_criteria=[
                    "BIA completed",
                    "Risk assessment done",
                    "Criteria defined",
                    "Results documented"
                ],
                common_gaps=[
                    "Missing or outdated BIA",
                    "Incomplete risk assessment",
                    "Undefined criteria"
                ]
            ),
            
            "8.3": ISORequirement(
                clause="8.3",
                title="Business continuity strategy",
                description="Determine business continuity strategy based on BIA and risk assessment",
                key_elements=[
                    "Strategy determination",
                    "Strategy options",
                    "Strategy selection",
                    "Strategy documentation"
                ],
                validation_criteria=[
                    "Strategy documented",
                    "Options evaluated",
                    "Selection justified",
                    "Implementation plan"
                ],
                common_gaps=[
                    "Missing BC strategy",
                    "Limited strategy options",
                    "Unjustified selection"
                ]
            ),
            
            "8.4": ISORequirement(
                clause="8.4",
                title="Business continuity procedures",
                description="Establish procedures to implement BC strategy",
                key_elements=[
                    "Procedure establishment",
                    "Procedure content",
                    "Procedure implementation",
                    "Procedure maintenance"
                ],
                validation_criteria=[
                    "Procedures documented",
                    "Content adequate",
                    "Implementation evidence",
                    "Maintenance process"
                ],
                common_gaps=[
                    "Missing BC procedures",
                    "Inadequate procedure content",
                    "No implementation evidence"
                ]
            ),
            
            "8.5": ISORequirement(
                clause="8.5",
                title="Exercise programme",
                description="Plan and conduct exercises to validate BC arrangements",
                key_elements=[
                    "Exercise planning",
                    "Exercise types",
                    "Exercise conduct",
                    "Exercise evaluation"
                ],
                validation_criteria=[
                    "Exercise program exists",
                    "Multiple exercise types",
                    "Regular conduct",
                    "Evaluation process"
                ],
                common_gaps=[
                    "No exercise program",
                    "Limited exercise types",
                    "Irregular exercise conduct"
                ]
            ),
            
            "9.1": ISORequirement(
                clause="9.1",
                title="Monitoring, measurement, analysis and evaluation",
                description="Monitor and measure BCMS performance",
                key_elements=[
                    "Monitoring requirements",
                    "Measurement methods",
                    "Analysis processes",
                    "Evaluation criteria"
                ],
                validation_criteria=[
                    "Monitoring plan exists",
                    "Methods documented",
                    "Analysis performed",
                    "Evaluation criteria set"
                ],
                common_gaps=[
                    "No monitoring plan",
                    "Undefined measurement methods",
                    "Missing analysis"
                ]
            ),
            
            "9.2": ISORequirement(
                clause="9.2",
                title="Internal audit",
                description="Conduct internal audits of BCMS",
                key_elements=[
                    "Audit program",
                    "Audit planning",
                    "Audit conduct",
                    "Audit reporting"
                ],
                validation_criteria=[
                    "Audit program exists",
                    "Planning documented",
                    "Audits conducted",
                    "Reports available"
                ],
                common_gaps=[
                    "No audit program",
                    "Poor audit planning",
                    "Irregular audits"
                ]
            ),
            
            "9.3": ISORequirement(
                clause="9.3",
                title="Management review",
                description="Conduct management review of BCMS",
                key_elements=[
                    "Review planning",
                    "Review inputs",
                    "Review outputs",
                    "Review frequency"
                ],
                validation_criteria=[
                    "Review process defined",
                    "Inputs specified",
                    "Outputs documented",
                    "Regular frequency"
                ],
                common_gaps=[
                    "No review process",
                    "Missing review inputs",
                    "Irregular reviews"
                ]
            ),
            
            "10.1": ISORequirement(
                clause="10.1",
                title="Nonconformity and corrective action",
                description="Address nonconformities and take corrective action",
                key_elements=[
                    "Nonconformity identification",
                    "Corrective action",
                    "Action effectiveness",
                    "Documentation"
                ],
                validation_criteria=[
                    "Identification process",
                    "Action procedures",
                    "Effectiveness review",
                    "Documentation maintained"
                ],
                common_gaps=[
                    "Poor nonconformity identification",
                    "Ineffective corrective actions",
                    "No effectiveness review"
                ]
            ),
            
            "10.2": ISORequirement(
                clause="10.2",
                title="Continual improvement",
                description="Continually improve BCMS suitability, adequacy and effectiveness",
                key_elements=[
                    "Improvement identification",
                    "Improvement implementation",
                    "Improvement monitoring",
                    "Improvement documentation"
                ],
                validation_criteria=[
                    "Improvement process exists",
                    "Implementation evidence",
                    "Monitoring mechanisms",
                    "Documentation maintained"
                ],
                common_gaps=[
                    "No improvement process",
                    "Missing implementation evidence",
                    "Poor improvement monitoring"
                ]
            )
        }
        
        return requirements
    
    def get_all_requirements(self) -> Dict[str, ISORequirement]:
        """Get all ISO 22301 requirements"""
        return self.requirements
    
    def get_requirement(self, clause: str) -> ISORequirement:
        """Get specific requirement by clause"""
        return self.requirements.get(clause)
    
    def get_requirements_by_category(self) -> Dict[str, List[ISORequirement]]:
        """Group requirements by main categories"""
        categories = {
            "Context": [],
            "Leadership": [],
            "Planning": [],
            "Support": [],
            "Operation": [],
            "Performance Evaluation": [],
            "Improvement": []
        }
        
        for req in self.requirements.values():
            if req.clause.startswith("4"):
                categories["Context"].append(req)
            elif req.clause.startswith("5"):
                categories["Leadership"].append(req)
            elif req.clause.startswith("6"):
                categories["Planning"].append(req)
            elif req.clause.startswith("7"):
                categories["Support"].append(req)
            elif req.clause.startswith("8"):
                categories["Operation"].append(req)
            elif req.clause.startswith("9"):
                categories["Performance Evaluation"].append(req)
            elif req.clause.startswith("10"):
                categories["Improvement"].append(req)
        
        return categories
    
    def get_critical_requirements(self) -> List[ISORequirement]:
        """Get most critical requirements for BCM"""
        critical_clauses = ["5.2", "8.2", "8.3", "8.4", "8.5", "9.1", "9.2"]
        return [self.requirements[clause] for clause in critical_clauses if clause in self.requirements]

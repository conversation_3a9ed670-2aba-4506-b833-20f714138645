{"document_title": "BCM Document", "document_path": "E:\\susan_chatbot_test\\Perpetuuiti_BCM_Plan.pdf", "validation_date": "2025-05-30T14:38:48.715702", "overall_compliance": 0.0, "findings": [{"iso_clause": "4.1", "iso_title": "Understanding the organization and its context", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.007983922958374023}, {"iso_clause": "4.2", "iso_title": "Understanding the needs and expectations of interested parties", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0020084381103515625}, {"iso_clause": "4.3", "iso_title": "Determining the scope of the business continuity management system", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009698867797851562}, {"iso_clause": "4.4", "iso_title": "Business continuity management system", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0010135173797607422}, {"iso_clause": "5.1", "iso_title": "Leadership and commitment", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.001016855239868164}, {"iso_clause": "5.2", "iso_title": "Policy", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0010006427764892578}, {"iso_clause": "5.3", "iso_title": "Organizational roles, responsibilities and authorities", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.001007080078125}, {"iso_clause": "6.1", "iso_title": "Actions to address risks and opportunities", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009932518005371094}, {"iso_clause": "6.2", "iso_title": "Business continuity objectives and planning to achieve them", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009989738464355469}, {"iso_clause": "7.1", "iso_title": "Resources", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0010018348693847656}, {"iso_clause": "7.2", "iso_title": "Competence", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009975433349609375}, {"iso_clause": "7.3", "iso_title": "Awareness", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0010204315185546875}, {"iso_clause": "7.4", "iso_title": "Communication", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009868144989013672}, {"iso_clause": "7.5", "iso_title": "Documented information", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009930133819580078}, {"iso_clause": "8.1", "iso_title": "Operational planning and control", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.001001119613647461}, {"iso_clause": "8.2", "iso_title": "Business impact analysis and risk assessment", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0010395050048828125}, {"iso_clause": "8.3", "iso_title": "Business continuity strategy", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009603500366210938}, {"iso_clause": "8.4", "iso_title": "Business continuity procedures", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009999275207519531}, {"iso_clause": "8.5", "iso_title": "Exercise programme", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0010001659393310547}, {"iso_clause": "9.1", "iso_title": "Monitoring, measurement, analysis and evaluation", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009999275207519531}, {"iso_clause": "9.2", "iso_title": "Internal audit", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.001001119613647461}, {"iso_clause": "9.3", "iso_title": "Management review", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009989738464355469}, {"iso_clause": "10.1", "iso_title": "Nonconformity and corrective action", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0009992122650146484}, {"iso_clause": "10.2", "iso_title": "Continual improvement", "status": "NOT_FOUND", "confidence": 0.9, "evidence": [], "gaps": [], "recommendations": [], "ai_analysis": "Error in AI analysis: Failed to connect to Ollama. Please check that Ollama is downloaded, running and accessible. https://ollama.com/download", "processing_time": 0.0010259151458740234}], "summary": {"status_counts": {"COMPLIANT": 0, "PARTIAL": 0, "NON_COMPLIANT": 0, "NOT_FOUND": 24}, "total_requirements": 24, "critical_gaps": [], "top_recommendations": [], "compliance_percentage": {"compliant": 0.0, "partial": 0.0, "non_compliant": 0.0, "not_found": 100.0}}, "processing_time": 0.521425724029541}
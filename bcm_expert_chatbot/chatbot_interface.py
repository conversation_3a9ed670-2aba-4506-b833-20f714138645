"""
BCM Expert Chatbot Interface
Interactive Q&A system for BCM guidance and document analysis.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

from .iso_standards import ISO22301Standards
from .document_processor import BCMDocumentProcessor, ProcessedDocument
from .validation_engine import BCMValidationEngine, ValidationResult

@dataclass
class ChatMessage:
    """Represents a chat message"""
    role: str  # "user" or "assistant"
    content: str
    timestamp: datetime
    context: Optional[Dict[str, Any]] = None

@dataclass
class ChatSession:
    """Represents a chat session"""
    session_id: str
    messages: List[ChatMessage]
    loaded_document: Optional[ProcessedDocument] = None
    validation_result: Optional[ValidationResult] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class BCMExpertChatbot:
    """Interactive BCM expert chatbot"""
    
    def __init__(self, model_name: str = "mistral"):
        self.model_name = model_name
        self.logger = self._setup_logging()
        self.iso_standards = ISO22301Standards()
        self.document_processor = BCMDocumentProcessor()
        self.validation_engine = BCMValidationEngine(model_name)
        self.sessions: Dict[str, ChatSession] = {}
        
        # Verify Ollama availability
        if not OLLAMA_AVAILABLE:
            raise ImportError("Ollama not available. Install with: pip install ollama")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the chatbot"""
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def create_session(self, session_id: str) -> ChatSession:
        """Create a new chat session"""
        session = ChatSession(
            session_id=session_id,
            messages=[],
            created_at=datetime.now()
        )
        
        # Add welcome message
        welcome_msg = ChatMessage(
            role="assistant",
            content=self._get_welcome_message(),
            timestamp=datetime.now()
        )
        session.messages.append(welcome_msg)
        
        self.sessions[session_id] = session
        return session
    
    def _get_welcome_message(self) -> str:
        """Get welcome message for new sessions"""
        return """🤖 **Welcome to BCM Expert Chatbot!**

I'm your AI-powered Business Continuity Management expert. I can help you with:

📋 **Document Validation**: Upload your BCM documents and I'll validate them against ISO 22301 standards
❓ **BCM Q&A**: Ask me anything about business continuity management
📊 **Compliance Guidance**: Get specific advice on ISO 22301 requirements
🔍 **Gap Analysis**: Identify what's missing in your BCM documentation

**Commands you can use:**
- `validate <file_path>` - Validate a BCM document
- `load <file_path>` - Load a document for Q&A
- `requirements` - List ISO 22301 requirements
- `help` - Show available commands

What would you like to know about BCM today?"""
    
    def chat(self, session_id: str, user_message: str) -> str:
        """Process user message and return response"""
        
        # Get or create session
        if session_id not in self.sessions:
            self.create_session(session_id)
        
        session = self.sessions[session_id]
        
        # Add user message to session
        user_msg = ChatMessage(
            role="user",
            content=user_message,
            timestamp=datetime.now()
        )
        session.messages.append(user_msg)
        
        # Process message
        response = self._process_message(session, user_message)
        
        # Add assistant response to session
        assistant_msg = ChatMessage(
            role="assistant",
            content=response,
            timestamp=datetime.now()
        )
        session.messages.append(assistant_msg)
        
        return response
    
    def _process_message(self, session: ChatSession, message: str) -> str:
        """Process user message and generate response"""
        
        message_lower = message.lower().strip()
        
        # Handle commands
        if message_lower.startswith('validate '):
            file_path = message[9:].strip()
            return self._handle_validate_command(session, file_path)
        
        elif message_lower.startswith('load '):
            file_path = message[5:].strip()
            return self._handle_load_command(session, file_path)
        
        elif message_lower == 'requirements':
            return self._handle_requirements_command()
        
        elif message_lower == 'help':
            return self._handle_help_command()
        
        elif message_lower in ['status', 'summary'] and session.validation_result:
            return self._handle_status_command(session)
        
        # Handle general BCM questions
        else:
            return self._handle_general_question(session, message)
    
    def _handle_validate_command(self, session: ChatSession, file_path: str) -> str:
        """Handle document validation command"""
        try:
            self.logger.info(f"Validating document: {file_path}")
            
            # Validate document
            validation_result = self.validation_engine.validate_document(file_path)
            session.validation_result = validation_result
            
            # Generate response
            response = f"""✅ **Document Validation Complete!**

📄 **Document**: {validation_result.document_title}
📊 **Overall Compliance**: {validation_result.overall_compliance:.1%}
⏱️ **Processing Time**: {validation_result.processing_time:.2f}s

**Compliance Breakdown:**
- ✅ Compliant: {validation_result.summary['status_counts']['COMPLIANT']} requirements
- ⚠️ Partial: {validation_result.summary['status_counts']['PARTIAL']} requirements  
- ❌ Non-Compliant: {validation_result.summary['status_counts']['NON_COMPLIANT']} requirements
- ❓ Not Found: {validation_result.summary['status_counts']['NOT_FOUND']} requirements

**Top Critical Gaps:**
"""
            
            for i, gap in enumerate(validation_result.summary['critical_gaps'][:5], 1):
                response += f"{i}. {gap}\\n"
            
            response += f"""
**Key Recommendations:**
"""
            
            for i, rec in enumerate(validation_result.summary['top_recommendations'][:5], 1):
                response += f"{i}. {rec}\\n"
            
            response += "\\nType `status` for detailed findings or ask me specific questions about the results!"
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error validating document: {e}")
            return f"❌ **Error validating document**: {e}\\n\\nPlease check the file path and try again."
    
    def _handle_load_command(self, session: ChatSession, file_path: str) -> str:
        """Handle document loading command"""
        try:
            self.logger.info(f"Loading document: {file_path}")
            
            # Process document
            document = self.document_processor.process_document(file_path)
            session.loaded_document = document
            
            response = f"""📄 **Document Loaded Successfully!**

**Title**: {document.title}
**Pages**: {document.total_pages}
**Words**: {document.total_words:,}
**Sections**: {len(document.sections)}

**Sections Found:**
"""
            
            for section in document.sections[:10]:  # Show first 10 sections
                response += f"- {section.title} ({section.section_type})\\n"
            
            if len(document.sections) > 10:
                response += f"... and {len(document.sections) - 10} more sections\\n"
            
            response += "\\nNow you can ask me questions about this document!"
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error loading document: {e}")
            return f"❌ **Error loading document**: {e}\\n\\nPlease check the file path and try again."
    
    def _handle_requirements_command(self) -> str:
        """Handle requirements listing command"""
        requirements = self.iso_standards.get_requirements_by_category()
        
        response = "📋 **ISO 22301:2019 Requirements**\\n\\n"
        
        for category, reqs in requirements.items():
            response += f"**{category}:**\\n"
            for req in reqs:
                response += f"- {req.clause}: {req.title}\\n"
            response += "\\n"
        
        response += "Ask me about any specific requirement for detailed information!"
        
        return response
    
    def _handle_help_command(self) -> str:
        """Handle help command"""
        return """🆘 **BCM Expert Chatbot Help**

**Commands:**
- `validate <file_path>` - Validate BCM document against ISO 22301
- `load <file_path>` - Load document for Q&A (PDF, DOCX, TXT)
- `requirements` - List all ISO 22301 requirements
- `status` - Show validation summary (after validation)
- `help` - Show this help message

**Example Questions:**
- "What is required for business impact analysis?"
- "How do I implement ISO 22301 clause 8.2?"
- "What are the key elements of a BCM policy?"
- "Tell me about testing and exercises in BCM"
- "What's missing in my risk assessment section?"

**File Paths:**
- Use full paths: `C:\\\\Documents\\\\bcm_plan.pdf`
- Or relative paths: `./documents/bcm_plan.pdf`

**Tips:**
- Load a document first for specific document questions
- Validate documents to get compliance analysis
- Ask follow-up questions for detailed explanations

How can I help you with BCM today?"""
    
    def _handle_status_command(self, session: ChatSession) -> str:
        """Handle status command for validation results"""
        if not session.validation_result:
            return "❌ No validation results available. Use `validate <file_path>` first."
        
        result = session.validation_result
        response = f"""📊 **Detailed Validation Status**

**Document**: {result.document_title}
**Validation Date**: {result.validation_date.strftime('%Y-%m-%d %H:%M:%S')}
**Overall Compliance**: {result.overall_compliance:.1%}

**Detailed Findings:**
"""
        
        # Group findings by status
        by_status = {}
        for finding in result.findings:
            if finding.status not in by_status:
                by_status[finding.status] = []
            by_status[finding.status].append(finding)
        
        for status in ["NON_COMPLIANT", "PARTIAL", "COMPLIANT", "NOT_FOUND"]:
            if status in by_status:
                status_emoji = {"COMPLIANT": "✅", "PARTIAL": "⚠️", "NON_COMPLIANT": "❌", "NOT_FOUND": "❓"}
                response += f"\\n**{status_emoji[status]} {status.replace('_', ' ').title()} ({len(by_status[status])}):**\\n"
                
                for finding in by_status[status][:5]:  # Show first 5
                    response += f"- {finding.iso_clause}: {finding.iso_title}\\n"
                
                if len(by_status[status]) > 5:
                    response += f"  ... and {len(by_status[status]) - 5} more\\n"
        
        response += "\\nAsk me about specific requirements for detailed analysis!"
        
        return response
    
    def _handle_general_question(self, session: ChatSession, question: str) -> str:
        """Handle general BCM questions using AI"""
        
        # Prepare context
        context = self._prepare_context(session, question)
        
        # Generate AI response
        ai_response = self._get_ai_response(question, context)
        
        return ai_response
    
    def _prepare_context(self, session: ChatSession, question: str) -> str:
        """Prepare context for AI response"""
        context = "You are an expert ISO 22301 Business Continuity Management consultant.\\n\\n"
        
        # Add ISO standards context
        context += "ISO 22301 Requirements Summary:\\n"
        requirements = self.iso_standards.get_critical_requirements()
        for req in requirements[:5]:  # Top 5 critical requirements
            context += f"- {req.clause}: {req.title} - {req.description}\\n"
        
        # Add document context if available
        if session.loaded_document:
            context += f"\\nLoaded Document: {session.loaded_document.title}\\n"
            context += f"Document has {len(session.loaded_document.sections)} sections.\\n"
            
            # Add relevant sections based on question
            relevant_sections = self._find_relevant_sections_for_question(session.loaded_document, question)
            if relevant_sections:
                context += "\\nRelevant Document Content:\\n"
                for section in relevant_sections[:3]:  # Top 3 relevant sections
                    context += f"Section: {section.title}\\n{section.content[:500]}...\\n\\n"
        
        # Add validation context if available
        if session.validation_result:
            context += f"\\nValidation Results Available - Overall Compliance: {session.validation_result.overall_compliance:.1%}\\n"
            context += f"Critical Gaps: {', '.join(session.validation_result.summary['critical_gaps'][:3])}\\n"
        
        return context
    
    def _find_relevant_sections_for_question(self, document: ProcessedDocument, question: str) -> List:
        """Find document sections relevant to the question"""
        question_lower = question.lower()
        relevant_sections = []
        
        for section in document.sections:
            section_text = f"{section.title} {section.content}".lower()
            
            # Simple keyword matching
            question_words = question_lower.split()
            matches = sum(1 for word in question_words if len(word) > 3 and word in section_text)
            
            if matches > 0:
                section.confidence = matches / len(question_words)
                relevant_sections.append(section)
        
        # Sort by relevance
        relevant_sections.sort(key=lambda x: x.confidence, reverse=True)
        
        return relevant_sections[:5]
    
    def _get_ai_response(self, question: str, context: str) -> str:
        """Get AI response using Ollama"""
        
        prompt = f"""{context}

User Question: {question}

Please provide a helpful, accurate response about Business Continuity Management. 
If the question is about ISO 22301, reference specific clauses and requirements.
If a document is loaded, use the document content to provide specific answers.
Keep responses practical and actionable.

Response:"""
        
        try:
            response = ollama.chat(
                model=self.model_name,
                messages=[{
                    'role': 'user',
                    'content': prompt
                }],
                options={
                    'temperature': 0.3,  # Balanced creativity and consistency
                    'top_p': 0.9
                }
            )
            
            return response['message']['content']
            
        except Exception as e:
            self.logger.error(f"Error getting AI response: {e}")
            return f"❌ I'm having trouble processing your question right now. Error: {e}\\n\\nPlease try rephrasing your question or check if Ollama is running."
    
    def get_session_history(self, session_id: str) -> List[ChatMessage]:
        """Get chat history for a session"""
        if session_id in self.sessions:
            return self.sessions[session_id].messages
        return []
    
    def clear_session(self, session_id: str) -> bool:
        """Clear a chat session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False
    
    def list_sessions(self) -> List[str]:
        """List all active sessions"""
        return list(self.sessions.keys())

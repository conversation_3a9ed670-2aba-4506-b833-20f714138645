"""
BCM Document Processor
Enhanced document processing for BCM validation using AI embeddings.
"""

import os
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# Document processing libraries
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

@dataclass
class DocumentSection:
    """Represents a section of the document"""
    title: str
    content: str
    section_type: str
    page_number: int
    word_count: int
    confidence: float
    embeddings: Optional[List[float]] = None

@dataclass
class ProcessedDocument:
    """Processed document with sections and metadata"""
    title: str
    file_path: str
    total_pages: int
    total_words: int
    sections: List[DocumentSection]
    full_text: str
    metadata: Dict[str, Any]
    processing_time: float

class BCMDocumentProcessor:
    """Enhanced document processor for BCM validation"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.section_patterns = self._initialize_section_patterns()
        self.bcm_keywords = self._initialize_bcm_keywords()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the processor"""
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def _initialize_section_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns to identify BCM sections"""
        return {
            "policy": [
                r"business continuity policy",
                r"bcm policy",
                r"policy statement",
                r"continuity policy"
            ],
            "scope": [
                r"scope",
                r"applicability",
                r"coverage",
                r"boundaries"
            ],
            "roles_responsibilities": [
                r"roles and responsibilities",
                r"organizational structure",
                r"bcm team",
                r"responsibilities"
            ],
            "risk_assessment": [
                r"risk assessment",
                r"risk analysis",
                r"threat assessment",
                r"vulnerability"
            ],
            "business_impact_analysis": [
                r"business impact analysis",
                r"bia",
                r"impact assessment",
                r"criticality analysis"
            ],
            "strategy": [
                r"business continuity strategy",
                r"recovery strategy",
                r"continuity strategy",
                r"response strategy"
            ],
            "procedures": [
                r"procedures",
                r"response procedures",
                r"recovery procedures",
                r"emergency procedures"
            ],
            "testing": [
                r"testing",
                r"exercises",
                r"drills",
                r"validation"
            ],
            "training": [
                r"training",
                r"awareness",
                r"education",
                r"competence"
            ],
            "maintenance": [
                r"maintenance",
                r"review",
                r"update",
                r"continuous improvement"
            ],
            "communication": [
                r"communication",
                r"notification",
                r"crisis communication",
                r"stakeholder communication"
            ],
            "resources": [
                r"resources",
                r"resource requirements",
                r"infrastructure",
                r"facilities"
            ]
        }
    
    def _initialize_bcm_keywords(self) -> List[str]:
        """Initialize BCM-related keywords for content analysis"""
        return [
            "business continuity", "bcm", "disaster recovery", "emergency response",
            "crisis management", "risk management", "business impact", "recovery time",
            "recovery point", "maximum tolerable downtime", "mtd", "rto", "rpo",
            "incident response", "emergency procedures", "backup", "alternate site",
            "vendor management", "supply chain", "stakeholder", "communication plan",
            "testing", "exercise", "drill", "validation", "maintenance", "review",
            "iso 22301", "standard", "compliance", "audit", "nonconformity"
        ]
    
    def process_document(self, file_path: str) -> ProcessedDocument:
        """Process a BCM document for validation"""
        import time
        start_time = time.time()
        
        self.logger.info(f"Processing document: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Document not found: {file_path}")
        
        # Extract text based on file type
        file_extension = Path(file_path).suffix.lower()
        
        if file_extension == '.pdf':
            text, metadata = self._extract_from_pdf(file_path)
        elif file_extension in ['.docx', '.doc']:
            text, metadata = self._extract_from_docx(file_path)
        elif file_extension == '.txt':
            text, metadata = self._extract_from_txt(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")
        
        if not text:
            raise ValueError("Could not extract text from document")
        
        # Process sections
        sections = self._extract_sections(text)
        
        # Generate embeddings for sections if Ollama is available
        if OLLAMA_AVAILABLE:
            sections = self._generate_embeddings(sections)
        
        # Calculate statistics
        total_words = len(text.split())
        processing_time = time.time() - start_time
        
        # Extract title
        title = self._extract_title(text, metadata)
        
        return ProcessedDocument(
            title=title,
            file_path=file_path,
            total_pages=metadata.get('pages', 1),
            total_words=total_words,
            sections=sections,
            full_text=text,
            metadata=metadata,
            processing_time=processing_time
        )
    
    def _extract_from_pdf(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Extract text from PDF file"""
        if not PDF_AVAILABLE:
            raise ImportError("PyPDF2 not available. Install with: pip install PyPDF2")
        
        text = ""
        metadata = {}
        
        try:
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                # Extract metadata
                metadata = {
                    'pages': len(reader.pages),
                    'title': reader.metadata.title if reader.metadata else None,
                    'author': reader.metadata.author if reader.metadata else None,
                    'creator': reader.metadata.creator if reader.metadata else None
                }
                
                # Extract text from all pages
                for page_num, page in enumerate(reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\\n--- Page {page_num + 1} ---\\n{page_text}\\n"
                    except Exception as e:
                        self.logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                        
        except Exception as e:
            raise Exception(f"Error reading PDF file: {e}")
        
        return text, metadata
    
    def _extract_from_docx(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Extract text from DOCX file"""
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx not available. Install with: pip install python-docx")
        
        try:
            doc = DocxDocument(file_path)
            
            # Extract text from paragraphs
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\\n"
            
            # Extract metadata
            metadata = {
                'pages': 1,  # DOCX doesn't have clear page concept
                'title': doc.core_properties.title,
                'author': doc.core_properties.author,
                'created': doc.core_properties.created
            }
            
            return text, metadata
            
        except Exception as e:
            raise Exception(f"Error reading DOCX file: {e}")
    
    def _extract_from_txt(self, file_path: str) -> Tuple[str, Dict[str, Any]]:
        """Extract text from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
            
            metadata = {
                'pages': 1,
                'title': Path(file_path).stem,
                'size': os.path.getsize(file_path)
            }
            
            return text, metadata
            
        except Exception as e:
            raise Exception(f"Error reading TXT file: {e}")
    
    def _extract_title(self, text: str, metadata: Dict[str, Any]) -> str:
        """Extract document title"""
        # Try metadata first
        if metadata.get('title'):
            return metadata['title']
        
        # Try to find title in first few lines
        lines = text.split('\\n')[:10]
        for line in lines:
            line = line.strip()
            if len(line) > 10 and len(line) < 100:
                # Check if it looks like a title
                if any(keyword in line.lower() for keyword in ['business continuity', 'bcm', 'plan', 'policy']):
                    return line
        
        return "BCM Document"
    
    def _extract_sections(self, text: str) -> List[DocumentSection]:
        """Extract and classify sections from document text"""
        sections = []
        
        # Split text into potential sections
        # Look for common section headers
        section_headers = re.findall(
            r'^\\s*(?:\\d+\\.?\\s*)?([A-Z][A-Za-z\\s]{5,50})\\s*$',
            text,
            re.MULTILINE
        )
        
        if not section_headers:
            # If no clear headers, split by paragraphs and group
            paragraphs = [p.strip() for p in text.split('\\n\\n') if p.strip()]
            sections = self._group_paragraphs_into_sections(paragraphs)
        else:
            sections = self._extract_sections_by_headers(text, section_headers)
        
        # Classify sections
        for section in sections:
            section.section_type = self._classify_section(section.title, section.content)
        
        return sections
    
    def _group_paragraphs_into_sections(self, paragraphs: List[str]) -> List[DocumentSection]:
        """Group paragraphs into logical sections"""
        sections = []
        current_section = None
        current_content = []
        
        for i, paragraph in enumerate(paragraphs):
            # Check if paragraph looks like a header
            if self._is_likely_header(paragraph):
                # Save previous section
                if current_section and current_content:
                    sections.append(DocumentSection(
                        title=current_section,
                        content='\\n\\n'.join(current_content),
                        section_type="unknown",
                        page_number=1,
                        word_count=len(' '.join(current_content).split()),
                        confidence=0.7
                    ))
                
                # Start new section
                current_section = paragraph
                current_content = []
            else:
                current_content.append(paragraph)
        
        # Add final section
        if current_section and current_content:
            sections.append(DocumentSection(
                title=current_section,
                content='\\n\\n'.join(current_content),
                section_type="unknown",
                page_number=1,
                word_count=len(' '.join(current_content).split()),
                confidence=0.7
            ))
        
        return sections
    
    def _extract_sections_by_headers(self, text: str, headers: List[str]) -> List[DocumentSection]:
        """Extract sections using identified headers"""
        sections = []
        
        for i, header in enumerate(headers):
            # Find header position in text
            header_pattern = re.escape(header)
            match = re.search(header_pattern, text, re.IGNORECASE)
            
            if match:
                start_pos = match.end()
                
                # Find next header or end of text
                if i + 1 < len(headers):
                    next_header_pattern = re.escape(headers[i + 1])
                    next_match = re.search(next_header_pattern, text[start_pos:], re.IGNORECASE)
                    end_pos = start_pos + next_match.start() if next_match else len(text)
                else:
                    end_pos = len(text)
                
                content = text[start_pos:end_pos].strip()
                
                sections.append(DocumentSection(
                    title=header,
                    content=content,
                    section_type="unknown",
                    page_number=1,
                    word_count=len(content.split()),
                    confidence=0.8
                ))
        
        return sections
    
    def _is_likely_header(self, text: str) -> bool:
        """Check if text looks like a section header"""
        text = text.strip()
        
        # Check length
        if len(text) < 5 or len(text) > 100:
            return False
        
        # Check if it's all caps or title case
        if text.isupper() or text.istitle():
            return True
        
        # Check if it starts with number
        if re.match(r'^\\d+\\.', text):
            return True
        
        # Check for BCM keywords
        if any(keyword in text.lower() for keyword in self.bcm_keywords[:10]):
            return True
        
        return False
    
    def _classify_section(self, title: str, content: str) -> str:
        """Classify section type based on title and content"""
        title_lower = title.lower()
        content_lower = content.lower()
        
        # Check each section type
        for section_type, patterns in self.section_patterns.items():
            for pattern in patterns:
                if re.search(pattern, title_lower) or re.search(pattern, content_lower):
                    return section_type
        
        return "general"
    
    def _generate_embeddings(self, sections: List[DocumentSection]) -> List[DocumentSection]:
        """Generate embeddings for sections using Ollama"""
        try:
            for section in sections:
                # Combine title and content for embedding
                text_for_embedding = f"{section.title}\\n{section.content[:1000]}"  # Limit content length
                
                response = ollama.embeddings(
                    model='nomic-embed-text',
                    prompt=text_for_embedding
                )
                
                section.embeddings = response['embedding']
                
        except Exception as e:
            self.logger.warning(f"Error generating embeddings: {e}")
        
        return sections
    
    def get_section_by_type(self, document: ProcessedDocument, section_type: str) -> List[DocumentSection]:
        """Get sections of specific type from document"""
        return [section for section in document.sections if section.section_type == section_type]
    
    def search_content(self, document: ProcessedDocument, query: str) -> List[DocumentSection]:
        """Search for content in document sections"""
        query_lower = query.lower()
        matching_sections = []
        
        for section in document.sections:
            if query_lower in section.title.lower() or query_lower in section.content.lower():
                matching_sections.append(section)
        
        return matching_sections
    
    def get_document_statistics(self, document: ProcessedDocument) -> Dict[str, Any]:
        """Get comprehensive document statistics"""
        section_types = {}
        for section in document.sections:
            section_types[section.section_type] = section_types.get(section.section_type, 0) + 1
        
        return {
            'total_pages': document.total_pages,
            'total_words': document.total_words,
            'total_sections': len(document.sections),
            'section_types': section_types,
            'avg_section_length': sum(s.word_count for s in document.sections) / len(document.sections) if document.sections else 0,
            'processing_time': document.processing_time
        }

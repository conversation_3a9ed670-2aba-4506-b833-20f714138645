"""
BCM Expert Chatbot - Example Usage
Demonstrates how to use the BCM validation system.
"""

import os
from bcm_expert_chatbot import BCMValidationEngine, BCMExpertChatbot, BCMReportGenerator

def example_document_validation():
    """Example: Validate a BCM document"""
    print("🔍 Example: Document Validation")
    print("=" * 50)
    
    # Path to your BCM document
    document_path = "Perpetuuiti_BCM_Plan.pdf"  # Update this path
    
    if not os.path.exists(document_path):
        print(f"❌ Document not found: {document_path}")
        print("Please update the document_path variable with a valid file path.")
        return
    
    try:
        # Initialize validation engine
        print("🤖 Initializing BCM Validation Engine...")
        validator = BCMValidationEngine()
        
        # Validate document
        print(f"📄 Validating document: {document_path}")
        result = validator.validate_document(document_path)
        
        # Print results
        print(f"\\n✅ Validation completed!")
        print(f"📊 Overall Compliance: {result.overall_compliance:.1%}")
        print(f"⏱️ Processing Time: {result.processing_time:.2f}s")
        
        # Print status breakdown
        summary = result.summary
        print(f"\\n📋 Status Breakdown:")
        print(f"   ✅ Compliant: {summary['status_counts']['COMPLIANT']}")
        print(f"   ⚠️ Partial: {summary['status_counts']['PARTIAL']}")
        print(f"   ❌ Non-Compliant: {summary['status_counts']['NON_COMPLIANT']}")
        print(f"   ❓ Not Found: {summary['status_counts']['NOT_FOUND']}")
        
        # Print top gaps
        if summary['critical_gaps']:
            print(f"\\n🚨 Top Critical Gaps:")
            for i, gap in enumerate(summary['critical_gaps'][:5], 1):
                print(f"   {i}. {gap}")
        
        # Generate reports
        print(f"\\n📄 Generating reports...")
        report_gen = BCMReportGenerator()
        
        # Executive summary
        exec_summary = report_gen.generate_executive_summary(result)
        with open("bcm_executive_summary.md", "w", encoding="utf-8") as f:
            f.write(exec_summary)
        print("   ✅ Executive summary saved: bcm_executive_summary.md")
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def example_chatbot_interaction():
    """Example: Interactive chatbot usage"""
    print("\\n🤖 Example: Chatbot Interaction")
    print("=" * 50)
    
    try:
        # Initialize chatbot
        chatbot = BCMExpertChatbot()
        
        # Create session
        session_id = "example_session"
        session = chatbot.create_session(session_id)
        
        # Example questions
        questions = [
            "What is required for business impact analysis?",
            "How do I implement ISO 22301 clause 8.2?",
            "What are the key elements of a BCM policy?",
            "requirements"  # List requirements command
        ]
        
        for question in questions:
            print(f"\\n👤 User: {question}")
            response = chatbot.chat(session_id, question)
            print(f"🤖 BCM Expert: {response[:200]}...")  # Show first 200 chars
            print("   [Response truncated for example]")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_iso_requirements():
    """Example: Working with ISO requirements"""
    print("\\n📋 Example: ISO 22301 Requirements")
    print("=" * 50)
    
    try:
        from bcm_expert_chatbot.iso_standards import ISO22301Standards
        
        # Initialize ISO standards
        iso_standards = ISO22301Standards()
        
        # Get all requirements
        all_requirements = iso_standards.get_all_requirements()
        print(f"📊 Total ISO 22301 requirements: {len(all_requirements)}")
        
        # Get requirements by category
        by_category = iso_standards.get_requirements_by_category()
        print(f"\\n📂 Requirements by category:")
        for category, reqs in by_category.items():
            print(f"   {category}: {len(reqs)} requirements")
        
        # Get critical requirements
        critical = iso_standards.get_critical_requirements()
        print(f"\\n🔥 Critical requirements ({len(critical)}):")
        for req in critical:
            print(f"   {req.clause}: {req.title}")
        
        # Show specific requirement details
        req_8_2 = iso_standards.get_requirement("8.2")
        if req_8_2:
            print(f"\\n📋 Example Requirement Details:")
            print(f"   Clause: {req_8_2.clause}")
            print(f"   Title: {req_8_2.title}")
            print(f"   Description: {req_8_2.description}")
            print(f"   Key Elements: {len(req_8_2.key_elements)}")
            print(f"   Common Gaps: {len(req_8_2.common_gaps)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run all examples"""
    print("🤖 BCM Expert Chatbot - Example Usage")
    print("=" * 60)
    
    # Check if Ollama is available
    try:
        import ollama
        models = ollama.list()
        print(f"✅ Ollama is running with {len(models['models'])} models")
    except Exception as e:
        print(f"❌ Ollama not available: {e}")
        print("Please ensure Ollama is installed and running with mistral and nomic-embed-text models")
        return
    
    # Run examples
    example_iso_requirements()
    
    # Uncomment to run document validation (update document path first)
    # example_document_validation()
    
    # Uncomment to run chatbot interaction
    # example_chatbot_interaction()
    
    print("\\n✅ Examples completed!")
    print("\\nTo run document validation:")
    print("1. Update document_path in example_document_validation()")
    print("2. Uncomment the function call in main()")
    print("\\nTo try the chatbot:")
    print("python -m bcm_expert_chatbot.main chat")

if __name__ == "__main__":
    main()

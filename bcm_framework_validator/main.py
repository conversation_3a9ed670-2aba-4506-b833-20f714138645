"""
Main BCM Framework Validator Application
AI-powered BCM document validation against your framework.
"""

import os
import sys
import argparse
from datetime import datetime
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from framework_loader import BCMFrameworkLoader
from ai_validator import AIBCMValidator
from report_generator import BCMReportGenerator

def main():
    """Main application function"""
    
    print("🤖 BCM Framework Validator - AI-Powered Document Analysis")
    print("=" * 60)
    print("Validates BCM documents against your framework using AI models")
    print()
    
    parser = argparse.ArgumentParser(
        description="AI-powered BCM document validation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Interactive mode
  python main.py --document "company_bcm.pdf"      # Validate specific document
  python main.py --framework "custom_framework.docx" --document "bcm.pdf"
  python main.py --check-setup                     # Check system setup
        """
    )
    
    parser.add_argument(
        "--document", "-d",
        help="Path to BCM document to validate"
    )
    
    parser.add_argument(
        "--framework", "-f",
        default="BCM_Plan_Framework.docx",
        help="Path to BCM framework document (default: BCM_Plan_Framework.docx)"
    )
    
    parser.add_argument(
        "--output", "-o",
        default="validation_reports",
        help="Output directory for reports (default: validation_reports)"
    )
    
    parser.add_argument(
        "--model", "-m",
        default="model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        help="Path to Mistral model file"
    )
    
    parser.add_argument(
        "--check-setup", "-c",
        action="store_true",
        help="Check system setup and requirements"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Run in interactive mode"
    )
    
    args = parser.parse_args()
    
    # Check setup if requested
    if args.check_setup:
        check_system_setup(args)
        return
    
    # Run interactive mode if no document specified or explicitly requested
    if args.interactive or not args.document:
        run_interactive_mode(args)
    else:
        # Direct validation mode
        validate_document(args.document, args.framework, args.model, args.output)

def check_system_setup(args):
    """Check system setup and requirements"""
    
    print("🔍 Checking System Setup...")
    print("-" * 30)
    
    # Check framework document
    if os.path.exists(args.framework):
        print(f"✅ Framework document found: {args.framework}")
        
        # Try to load framework
        try:
            loader = BCMFrameworkLoader(args.framework)
            framework = loader.load_framework()
            print(f"✅ Framework loaded successfully: {framework.total_topics} topics")
        except Exception as e:
            print(f"❌ Error loading framework: {e}")
    else:
        print(f"❌ Framework document not found: {args.framework}")
    
    # Check Mistral model
    if os.path.exists(args.model):
        print(f"✅ Mistral model found: {args.model}")
        print(f"   Size: {os.path.getsize(args.model) / (1024**3):.1f} GB")
    else:
        print(f"❌ Mistral model not found: {args.model}")
        print("   Download from: https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF")
    
    # Check Python dependencies
    print("\n📦 Checking Python Dependencies...")
    
    required_packages = [
        ('docx', 'python-docx'),
        ('requests', 'requests'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
    ]
    
    optional_packages = [
        ('llama_cpp', 'llama-cpp-python'),
        ('PyPDF2', 'PyPDF2'),
    ]
    
    for module, package in required_packages:
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Install with: pip install {package}")
    
    print("\nOptional packages:")
    for module, package in optional_packages:
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"⚠️ {package} - Install with: pip install {package}")
    
    # Check Ollama
    print("\n🦙 Checking Ollama Setup...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama running with {len(models)} models")
            
            # Check for required models
            model_names = [m['name'] for m in models]
            if any('nomic' in name for name in model_names):
                print("✅ Nomic embeddings model available")
            else:
                print("⚠️ Nomic embeddings model not found - Install with: ollama pull nomic-embed-text")
                
            if any('mistral' in name for name in model_names):
                print("✅ Mistral model available in Ollama")
            else:
                print("⚠️ Mistral model not found in Ollama - Install with: ollama pull mistral")
        else:
            print("❌ Ollama not responding")
    except Exception as e:
        print(f"❌ Ollama not available: {e}")
        print("   Install from: https://ollama.ai")
    
    print("\n📁 Output Directory...")
    os.makedirs(args.output, exist_ok=True)
    print(f"✅ Output directory ready: {args.output}")
    
    print("\n🎯 Setup Summary:")
    print("For full functionality, ensure:")
    print("1. BCM framework document is available")
    print("2. Mistral model is downloaded")
    print("3. Required Python packages are installed")
    print("4. Ollama is running with required models")

def run_interactive_mode(args):
    """Run interactive validation mode"""
    
    print("🎯 Interactive BCM Validation Mode")
    print("-" * 35)
    
    while True:
        print("\nOptions:")
        print("1. Validate BCM document")
        print("2. Check framework topics")
        print("3. Check system setup")
        print("4. Exit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == "1":
            # Get document path
            doc_path = input("Enter path to BCM document: ").strip().strip('"')
            
            if not doc_path:
                print("❌ No document path provided")
                continue
                
            if not os.path.exists(doc_path):
                print(f"❌ Document not found: {doc_path}")
                continue
            
            # Validate document
            try:
                validate_document(doc_path, args.framework, args.model, args.output)
            except Exception as e:
                print(f"❌ Validation error: {e}")
        
        elif choice == "2":
            # Show framework topics
            try:
                loader = BCMFrameworkLoader(args.framework)
                framework = loader.load_framework()
                print(loader.get_framework_summary())
                
                # Show topics by category
                for category, topics in framework.categories.items():
                    print(f"\n{category}:")
                    for topic in topics[:5]:  # Show first 5
                        print(f"  • {topic.title}")
                    if len(topics) > 5:
                        print(f"  ... and {len(topics) - 5} more")
                        
            except Exception as e:
                print(f"❌ Error loading framework: {e}")
        
        elif choice == "3":
            check_system_setup(args)
        
        elif choice == "4":
            print("👋 Thank you for using BCM Framework Validator!")
            break
        
        else:
            print("❌ Invalid option. Please try again.")

def validate_document(document_path: str, framework_path: str, model_path: str, output_dir: str):
    """Validate a BCM document"""
    
    print(f"\n🚀 Starting BCM Validation...")
    print(f"📄 Document: {document_path}")
    print(f"📋 Framework: {framework_path}")
    print(f"🤖 Model: {model_path}")
    print()
    
    try:
        # Initialize validator
        print("🔧 Initializing AI validator...")
        validator = AIBCMValidator(framework_path, model_path)
        
        # Perform validation
        print("🔍 Performing AI-powered validation...")
        report = validator.validate_document(document_path)
        
        # Generate reports
        print("📊 Generating comprehensive reports...")
        report_generator = BCMReportGenerator(output_dir)
        generated_files = report_generator.generate_all_reports(report)
        
        # Show results
        print("\n" + "="*60)
        print("🎉 VALIDATION COMPLETED!")
        print("="*60)
        print(f"📄 Document: {report.document_name}")
        print(f"📊 Overall Score: {report.overall_score:.1f}%")
        print(f"✅ Found: {report.found_topics} topics")
        print(f"⚠️ Partial: {report.partial_topics} topics")
        print(f"❌ Missing: {report.missing_topics} topics")
        
        print(f"\n📁 Reports Generated:")
        for report_type, file_path in generated_files.items():
            print(f"  • {report_type}: {file_path}")
        
        print(f"\n🎯 Next Steps:")
        if report.overall_score >= 80:
            print("✅ Excellent! Your BCM plan is well-structured.")
            print("   Focus on the quick wins to achieve perfection.")
        elif report.overall_score >= 60:
            print("⚠️ Good foundation, but needs improvement.")
            print("   Review the action plan for priority items.")
        else:
            print("❌ Significant gaps identified.")
            print("   Start with the executive summary and action plan.")
        
        print(f"\n📋 Priority Actions:")
        for i, gap in enumerate(report.priority_gaps[:3], 1):
            print(f"  {i}. {gap}")
        
        print(f"\n💡 Quick Wins:")
        for i, win in enumerate(report.quick_wins[:3], 1):
            print(f"  {i}. {win}")
        
        print(f"\n📖 For detailed analysis, see: {generated_files.get('detailed_analysis', 'detailed report')}")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        print("\nTroubleshooting:")
        print("1. Check that all required files exist")
        print("2. Ensure Ollama is running (if using)")
        print("3. Verify Python dependencies are installed")
        print("4. Run --check-setup for detailed diagnostics")

if __name__ == "__main__":
    main()

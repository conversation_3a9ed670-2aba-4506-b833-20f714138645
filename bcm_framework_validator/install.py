"""
Installation script for BCM Framework Validator
Helps set up the system with all required dependencies and models.
"""

import os
import sys
import subprocess
import requests
from pathlib import Path

def install_python_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    try:
        # Install core requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "python-docx", "requests", "matplotlib", "seaborn"
        ])
        print("✅ Core dependencies installed")
        
        # Try to install optional dependencies
        optional_packages = ["llama-cpp-python", "PyPDF2", "pandas", "openpyxl"]
        
        for package in optional_packages:
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package
                ])
                print(f"✅ {package} installed")
            except subprocess.CalledProcessError:
                print(f"⚠️ {package} installation failed (optional)")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Dependency installation failed: {e}")
        return False

def check_ollama_installation():
    """Check if Ollama is installed and running"""
    print("\n🦙 Checking Ollama installation...")
    
    try:
        # Check if <PERSON>llama is running
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running")
            return True
        else:
            print("❌ Ollama is not responding")
            return False
    except requests.exceptions.RequestException:
        print("❌ Ollama is not running or not installed")
        print("📥 Please install Ollama from: https://ollama.ai")
        return False

def install_ollama_models():
    """Install required Ollama models"""
    print("\n🤖 Installing Ollama models...")
    
    models = [
        ("nomic-embed-text", "Embeddings model for semantic search"),
        ("mistral", "Mistral model for document analysis")
    ]
    
    success = True
    
    for model_name, description in models:
        print(f"📥 Installing {model_name} ({description})...")
        try:
            result = subprocess.run(
                ["ollama", "pull", model_name],
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                print(f"✅ {model_name} installed successfully")
            else:
                print(f"❌ Failed to install {model_name}: {result.stderr}")
                success = False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {model_name} installation timed out")
            success = False
        except FileNotFoundError:
            print("❌ Ollama command not found. Please install Ollama first.")
            success = False
        except Exception as e:
            print(f"❌ Error installing {model_name}: {e}")
            success = False
    
    return success

def check_mistral_model():
    """Check if Mistral model file exists"""
    print("\n🔍 Checking Mistral model file...")
    
    model_paths = [
        "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        "../model/mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    ]
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            size_gb = os.path.getsize(model_path) / (1024**3)
            print(f"✅ Mistral model found: {model_path} ({size_gb:.1f} GB)")
            return True
    
    print("⚠️ Mistral model file not found")
    print("📥 Please download from:")
    print("   https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF")
    print("   File: mistral-7b-instruct-v0.1.Q4_K_M.gguf")
    print("   Place in: model/ directory")
    return False

def check_framework_document():
    """Check if BCM framework document exists"""
    print("\n📋 Checking BCM framework document...")
    
    framework_paths = [
        "BCM_Plan_Framework.docx",
        "../BCM_Plan_Framework.docx"
    ]
    
    for framework_path in framework_paths:
        if os.path.exists(framework_path):
            print(f"✅ Framework document found: {framework_path}")
            return True
    
    print("❌ BCM_Plan_Framework.docx not found")
    print("📄 Please ensure your BCM framework document is available")
    return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "validation_reports",
        "model"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Directory created: {directory}")

def run_system_test():
    """Run system test"""
    print("\n🧪 Running system test...")
    
    try:
        # Import and run test
        from test_validator import main as test_main
        test_main()
        return True
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 BCM Framework Validator - Installation Setup")
    print("=" * 60)
    print("This script will help you set up the BCM Framework Validator")
    print()
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ is required")
        return
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Installation steps
    steps = [
        ("Creating directories", create_directories),
        ("Installing Python dependencies", install_python_dependencies),
        ("Checking framework document", check_framework_document),
        ("Checking Mistral model", check_mistral_model),
        ("Checking Ollama", check_ollama_installation),
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"❌ Step failed: {e}")
            results.append((step_name, False))
    
    # Install Ollama models if Ollama is available
    if any(name == "Checking Ollama" and result for name, result in results):
        print(f"\n{'='*20} Installing Ollama models {'='*20}")
        try:
            result = install_ollama_models()
            results.append(("Installing Ollama models", result))
        except Exception as e:
            print(f"❌ Ollama model installation failed: {e}")
            results.append(("Installing Ollama models", False))
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 INSTALLATION SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    for step_name, result in results:
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{status} {step_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} steps completed successfully")
    
    if passed >= len(results) - 1:  # Allow one optional failure
        print("\n🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Run system test: python test_validator.py")
        print("2. Try validation: python main.py --check-setup")
        print("3. Validate document: python main.py --document 'your_bcm.pdf'")
        
        # Offer to run system test
        if input("\nRun system test now? (y/n): ").lower().startswith('y'):
            run_system_test()
    else:
        print("\n⚠️ Installation incomplete. Please address the failed steps.")
        print("\nCommon issues and solutions:")
        print("1. Framework document: Ensure BCM_Plan_Framework.docx is available")
        print("2. Mistral model: Download from HuggingFace and place in model/ directory")
        print("3. Ollama: Install from https://ollama.ai and start the service")
        print("4. Dependencies: Ensure you have internet connection for pip installs")

if __name__ == "__main__":
    main()

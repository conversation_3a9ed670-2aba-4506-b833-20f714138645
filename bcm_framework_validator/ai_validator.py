"""
AI-Powered BCM Validator
Uses Ollama and Mistral models to validate BCM documents against the framework.
"""

import os
import json
import time
import requests
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from framework_loader import <PERSON><PERSON>rameworkLoader, BCMFramework, FrameworkTopic

@dataclass
class ValidationFinding:
    """Represents a validation finding"""
    topic_id: int
    topic_title: str
    category: str
    status: str  # "FOUND", "MISSING", "PARTIAL", "UNCLEAR"
    confidence: float  # 0.0 to 1.0
    evidence: str  # Text evidence found in document
    gaps: List[str]  # Specific gaps identified
    recommendations: List[str]  # Actionable recommendations
    ai_analysis: str  # Detailed AI analysis

@dataclass
class ValidationReport:
    """Complete validation report"""
    document_name: str
    validation_date: datetime
    framework_version: str
    overall_score: float  # 0.0 to 100.0
    total_topics: int
    found_topics: int
    missing_topics: int
    partial_topics: int
    findings: List[ValidationFinding]
    category_scores: Dict[str, float]
    executive_summary: str
    priority_gaps: List[str]
    quick_wins: List[str]

class OllamaEmbeddings:
    """Ollama embeddings for semantic search"""
    
    def __init__(self, model_name: str = "nomic-embed-text", base_url: str = "http://localhost:11434"):
        self.model_name = model_name
        self.base_url = base_url
        
    def get_embeddings(self, text: str) -> List[float]:
        """Get embeddings for text"""
        try:
            response = requests.post(
                f"{self.base_url}/api/embeddings",
                json={
                    "model": self.model_name,
                    "prompt": text
                },
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json().get("embedding", [])
            else:
                print(f"⚠️ Ollama embeddings error: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"⚠️ Ollama connection error: {e}")
            return []

class MistralAnalyzer:
    """Mistral model for document analysis"""
    
    def __init__(self, model_path: str = "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf"):
        self.model_path = model_path
        self.model_loaded = False
        
        # Try to use llama-cpp-python if available
        try:
            from llama_cpp import Llama
            self.llama = Llama(
                model_path=model_path,
                n_ctx=4096,
                n_threads=4,
                verbose=False
            )
            self.model_loaded = True
            print("✅ Mistral model loaded successfully")
        except ImportError:
            print("⚠️ llama-cpp-python not available, will use Ollama fallback")
            self.llama = None
        except Exception as e:
            print(f"⚠️ Error loading Mistral model: {e}")
            self.llama = None
    
    def analyze_text(self, prompt: str, max_tokens: int = 1000) -> str:
        """Analyze text using Mistral model"""
        
        if self.llama:
            try:
                response = self.llama(
                    prompt,
                    max_tokens=max_tokens,
                    temperature=0.1,
                    top_p=0.9,
                    stop=["</analysis>", "\n\n---"]
                )
                return response['choices'][0]['text'].strip()
            except Exception as e:
                print(f"⚠️ Mistral analysis error: {e}")
                return self._fallback_analysis(prompt)
        else:
            return self._fallback_analysis(prompt)
    
    def _fallback_analysis(self, prompt: str) -> str:
        """Fallback analysis using Ollama"""
        try:
            response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": "mistral",
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.1,
                        "top_p": 0.9
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json().get("response", "Analysis not available")
            else:
                return "Analysis not available - model error"
                
        except Exception as e:
            print(f"⚠️ Ollama fallback error: {e}")
            return "Analysis not available - connection error"

class AIBCMValidator:
    """Main AI-powered BCM validator"""
    
    def __init__(self, framework_path: str = "BCM_Plan_Framework.docx", 
                 mistral_path: str = "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf"):
        
        # Load framework
        self.framework_loader = BCMFrameworkLoader(framework_path)
        self.framework = None
        
        # Initialize AI models
        self.embeddings = OllamaEmbeddings()
        self.analyzer = MistralAnalyzer(mistral_path)
        
        print("🤖 AI BCM Validator initialized")
    
    def load_framework(self) -> BCMFramework:
        """Load the BCM framework"""
        self.framework = self.framework_loader.load_framework()
        return self.framework
    
    def validate_document(self, document_path: str, document_text: str = None) -> ValidationReport:
        """Validate a BCM document against the framework"""
        
        if not self.framework:
            self.load_framework()
        
        print(f"🔍 Starting AI validation of: {document_path}")
        start_time = time.time()
        
        # Load document text if not provided
        if not document_text:
            document_text = self._extract_document_text(document_path)
        
        if not document_text:
            raise ValueError("Could not extract text from document")
        
        print(f"📄 Document loaded: {len(document_text)} characters")
        
        # Validate each category
        findings = []
        category_scores = {}
        
        for category, topics in self.framework.categories.items():
            print(f"🔍 Analyzing category: {category}")
            
            category_findings = self._validate_category(category, topics, document_text)
            findings.extend(category_findings)
            
            # Calculate category score
            if category_findings:
                found_count = sum(1 for f in category_findings if f.status == "FOUND")
                partial_count = sum(1 for f in category_findings if f.status == "PARTIAL")
                total_count = len(category_findings)
                
                category_score = ((found_count * 1.0) + (partial_count * 0.5)) / total_count * 100
                category_scores[category] = category_score
            else:
                category_scores[category] = 0.0
        
        # Calculate overall metrics
        total_topics = len(findings)
        found_topics = sum(1 for f in findings if f.status == "FOUND")
        missing_topics = sum(1 for f in findings if f.status == "MISSING")
        partial_topics = sum(1 for f in findings if f.status == "PARTIAL")
        
        overall_score = ((found_topics * 1.0) + (partial_topics * 0.5)) / total_topics * 100 if total_topics > 0 else 0
        
        # Generate executive summary and recommendations
        executive_summary = self._generate_executive_summary(findings, overall_score)
        priority_gaps = self._identify_priority_gaps(findings)
        quick_wins = self._identify_quick_wins(findings)
        
        # Create validation report
        report = ValidationReport(
            document_name=os.path.basename(document_path),
            validation_date=datetime.now(),
            framework_version="1.0",
            overall_score=overall_score,
            total_topics=total_topics,
            found_topics=found_topics,
            missing_topics=missing_topics,
            partial_topics=partial_topics,
            findings=findings,
            category_scores=category_scores,
            executive_summary=executive_summary,
            priority_gaps=priority_gaps,
            quick_wins=quick_wins
        )
        
        elapsed_time = time.time() - start_time
        print(f"✅ Validation completed in {elapsed_time:.1f} seconds")
        print(f"📊 Overall Score: {overall_score:.1f}% ({found_topics}/{total_topics} topics found)")
        
        return report
    
    def _validate_category(self, category: str, topics: List[FrameworkTopic], 
                          document_text: str) -> List[ValidationFinding]:
        """Validate a specific category of topics"""
        
        findings = []
        
        # Get category-specific validation prompt
        prompts = self.framework_loader.get_validation_prompts()
        category_prompt = prompts.get(category, "Analyze if this category is covered in the document.")
        
        # Create analysis prompt
        analysis_prompt = f"""
<analysis_task>
You are a BCM (Business Continuity Management) expert. Analyze the following document text to determine if it covers the required topics for the category: {category}

Category Description: {category_prompt}

Required Topics in this Category:
{chr(10).join([f"- {topic.title}" for topic in topics])}

Document Text to Analyze:
{document_text[:8000]}...

For each required topic, determine:
1. STATUS: FOUND (clearly present), PARTIAL (mentioned but incomplete), MISSING (not found), UNCLEAR (ambiguous)
2. CONFIDENCE: 0.0 to 1.0 (how confident you are in your assessment)
3. EVIDENCE: Specific text from document that supports your finding
4. GAPS: What specific elements are missing or incomplete
5. RECOMMENDATIONS: Specific actionable steps to improve

Provide your analysis in this format for each topic:
TOPIC: [topic title]
STATUS: [FOUND/PARTIAL/MISSING/UNCLEAR]
CONFIDENCE: [0.0-1.0]
EVIDENCE: [specific text quotes or "None found"]
GAPS: [list of missing elements]
RECOMMENDATIONS: [specific actionable steps]
---
</analysis_task>

Analysis:
"""
        
        # Get AI analysis
        ai_response = self.analyzer.analyze_text(analysis_prompt, max_tokens=2000)
        
        # Parse AI response and create findings
        findings = self._parse_ai_analysis(topics, ai_response, category)
        
        return findings
    
    def _parse_ai_analysis(self, topics: List[FrameworkTopic], 
                          ai_response: str, category: str) -> List[ValidationFinding]:
        """Parse AI analysis response into structured findings"""
        
        findings = []
        
        # Split response by topic separators
        topic_analyses = ai_response.split("---")
        
        for i, topic in enumerate(topics):
            # Try to find corresponding analysis
            topic_analysis = ""
            for analysis in topic_analyses:
                if topic.title.lower() in analysis.lower():
                    topic_analysis = analysis
                    break
            
            # If no specific analysis found, create default
            if not topic_analysis and i < len(topic_analyses):
                topic_analysis = topic_analyses[i] if i < len(topic_analyses) else ""
            
            # Parse the analysis
            status = self._extract_field(topic_analysis, "STATUS", "UNCLEAR")
            confidence = float(self._extract_field(topic_analysis, "CONFIDENCE", "0.5"))
            evidence = self._extract_field(topic_analysis, "EVIDENCE", "No specific evidence found")
            gaps_text = self._extract_field(topic_analysis, "GAPS", "Analysis needed")
            recommendations_text = self._extract_field(topic_analysis, "RECOMMENDATIONS", "Review and update needed")
            
            # Convert to lists
            gaps = [gap.strip() for gap in gaps_text.split(",") if gap.strip()]
            recommendations = [rec.strip() for rec in recommendations_text.split(",") if rec.strip()]
            
            finding = ValidationFinding(
                topic_id=topic.id,
                topic_title=topic.title,
                category=category,
                status=status,
                confidence=confidence,
                evidence=evidence,
                gaps=gaps,
                recommendations=recommendations,
                ai_analysis=topic_analysis
            )
            
            findings.append(finding)
        
        return findings
    
    def _extract_field(self, text: str, field_name: str, default: str) -> str:
        """Extract a field value from AI analysis text"""
        
        lines = text.split("\n")
        for line in lines:
            if line.strip().startswith(f"{field_name}:"):
                return line.split(":", 1)[1].strip()
        
        return default
    
    def _extract_document_text(self, document_path: str) -> str:
        """Extract text from document"""
        
        try:
            if document_path.endswith('.pdf'):
                # Try to extract PDF text
                try:
                    import PyPDF2
                    with open(document_path, 'rb') as file:
                        reader = PyPDF2.PdfReader(file)
                        text = ""
                        for page in reader.pages:
                            text += page.extract_text() + "\n"
                        return text
                except ImportError:
                    print("⚠️ PyPDF2 not available for PDF extraction")
                    return ""
            
            elif document_path.endswith('.docx'):
                # Extract Word document text
                doc = docx.Document(document_path)
                text = ""
                for para in doc.paragraphs:
                    text += para.text + "\n"
                return text
            
            elif document_path.endswith('.txt'):
                # Read text file
                with open(document_path, 'r', encoding='utf-8') as file:
                    return file.read()
            
            else:
                print(f"⚠️ Unsupported file format: {document_path}")
                return ""
                
        except Exception as e:
            print(f"⚠️ Error extracting text from {document_path}: {e}")
            return ""
    
    def _generate_executive_summary(self, findings: List[ValidationFinding], overall_score: float) -> str:
        """Generate executive summary"""
        
        found_count = sum(1 for f in findings if f.status == "FOUND")
        missing_count = sum(1 for f in findings if f.status == "MISSING")
        partial_count = sum(1 for f in findings if f.status == "PARTIAL")
        
        summary = f"""
Executive Summary:
=================

Overall BCM Compliance Score: {overall_score:.1f}%

Key Findings:
• {found_count} topics are fully covered
• {partial_count} topics are partially covered  
• {missing_count} topics are missing or unclear

"""
        
        if overall_score >= 80:
            summary += "✅ GOOD: Your BCM plan has strong coverage of most required topics."
        elif overall_score >= 60:
            summary += "⚠️ MODERATE: Your BCM plan covers most topics but needs improvement in several areas."
        else:
            summary += "❌ NEEDS IMPROVEMENT: Your BCM plan has significant gaps that should be addressed."
        
        return summary
    
    def _identify_priority_gaps(self, findings: List[ValidationFinding]) -> List[str]:
        """Identify priority gaps that need immediate attention"""
        
        priority_gaps = []
        
        # Critical topics that should be prioritized
        critical_topics = [
            "Crisis Management Team",
            "Roles and responsibilities", 
            "Communication Guidelines",
            "Business Impact Analysis",
            "Risk Management"
        ]
        
        for finding in findings:
            if (finding.status in ["MISSING", "UNCLEAR"] and 
                any(critical in finding.topic_title for critical in critical_topics)):
                priority_gaps.append(f"{finding.topic_title}: {finding.gaps[0] if finding.gaps else 'Needs to be defined'}")
        
        return priority_gaps[:5]  # Top 5 priority gaps
    
    def _identify_quick_wins(self, findings: List[ValidationFinding]) -> List[str]:
        """Identify quick wins - easy improvements"""
        
        quick_wins = []
        
        for finding in findings:
            if finding.status == "PARTIAL" and finding.confidence > 0.7:
                if finding.recommendations:
                    quick_wins.append(f"{finding.topic_title}: {finding.recommendations[0]}")
        
        return quick_wins[:5]  # Top 5 quick wins

if __name__ == "__main__":
    # Test the validator
    validator = AIBCMValidator()
    
    # Test with a sample document
    test_doc = "Perpetuuiti_BCM_Plan.pdf"
    if os.path.exists(test_doc):
        report = validator.validate_document(test_doc)
        print(f"\nValidation completed!")
        print(f"Overall Score: {report.overall_score:.1f}%")
        print(f"Found: {report.found_topics}, Missing: {report.missing_topics}")
    else:
        print(f"Test document not found: {test_doc}")

# BCM Framework Validator

🤖 **AI-Powered BCM Document Validation Against Your Framework**

This system uses advanced AI models (Ollama + Mistral) to intelligently validate company BCM documents against your BCM Plan Framework, providing detailed, actionable feedback in simple language.

## 🎯 What It Does

- **Loads your BCM Plan Framework** as the reference standard (75 topics)
- **Uses AI models** to understand document content semantically (not just keywords)
- **Validates company BCM documents** against all framework topics
- **Provides topic-specific feedback** on what's missing or incomplete
- **Generates actionable reports** in simple language for employees
- **Supports Q&A mode** for interactive BCM guidance

## 🏗️ System Architecture

```
bcm_framework_validator/
├── framework_loader.py      # Loads and processes your BCM framework
├── ai_validator.py          # AI-powered validation engine
├── report_generator.py      # Comprehensive report generation
├── main.py                  # Command-line interface
├── __init__.py             # Package initialization
└── README.md               # This documentation
```

## 🚀 Quick Start

### 1. Setup Requirements

```bash
# Install Python dependencies
pip install python-doc<PERSON> requests matplot<PERSON><PERSON> seaborn

# Optional: For better performance
pip install llama-cpp-python PyPDF2

# Install and start Ollama
# Download from: https://ollama.ai
ollama pull nomic-embed-text
ollama pull mistral
```

### 2. Prepare Your Files

Ensure you have:
- `BCM_Plan_Framework.docx` (your framework document)
- `model/mistral-7b-instruct-v0.1.Q4_K_M.gguf` (Mistral model)
- Company BCM document to validate (PDF/DOCX)

### 3. Run Validation

```bash
# Interactive mode
python main.py

# Direct validation
python main.py --document "company_bcm.pdf"

# Check system setup
python main.py --check-setup
```

## 📋 Framework Topics Covered

Your BCM Plan Framework includes **75 comprehensive topics** across 9 categories:

### 🏛️ Policy & Governance
- Policy Statement, Objectives, Scope, Applicability

### 👥 Personnel & Organization  
- Crisis Management Team, Roles & Responsibilities, Contact Info

### 🚨 Crisis Management
- Fire, Power Failure, Security Breaches, Equipment Failure, etc.

### 📢 Communication
- Internal/External Guidelines, Media Handling, Stakeholder Notification

### 🔄 Recovery & Testing
- Return to Normalcy, Post-Incident Review, Testing Scenarios

### 📊 Risk & Impact Analysis
- Business Impact Analysis, Risk Management, Service Level Agreements

### 📄 Documentation & Forms
- Templates, Contact Lists, Recovery Forms

### 🎓 Training & Exercises
- Awareness Programs, Exercise Schedules, Performance Indicators

### ⚖️ Legal & Compliance
- Insurance, Legal Actions, Vendor Management

## 🤖 AI Analysis Features

### Intelligent Content Understanding
- **Semantic Analysis**: Understands meaning, not just keywords
- **Context Awareness**: Recognizes related concepts and synonyms
- **Evidence Extraction**: Finds specific text supporting findings
- **Confidence Scoring**: Rates certainty of each assessment

### Advanced Validation Logic
- **Topic Mapping**: Maps document content to framework topics
- **Gap Identification**: Identifies specific missing elements
- **Completeness Assessment**: Evaluates partial vs. complete coverage
- **Recommendation Generation**: Provides actionable improvement steps

## 📊 Generated Reports

### 1. Executive Summary
- Overall compliance score
- Category performance breakdown
- Immediate priorities
- Quick wins

### 2. Detailed Analysis
- Topic-by-topic findings
- Evidence and confidence scores
- Specific gaps and recommendations
- AI analysis details

### 3. Action Plan
- Phased improvement roadmap
- Priority assignments
- Timeline and milestones
- Resource requirements

### 4. Gap Analysis
- Detailed gap assessment
- Impact evaluation
- Remediation priorities
- Compliance roadmap

### 5. Visual Charts
- Compliance score gauges
- Category performance bars
- Status distribution
- Improvement projections

### 6. Compliance Matrix (CSV)
- Structured data export
- Integration-ready format
- Detailed findings table

## 💡 Example Usage

### Basic Validation
```python
from bcm_framework_validator import AIBCMValidator

# Initialize validator
validator = AIBCMValidator()

# Validate document
report = validator.validate_document("company_bcm.pdf")

# Check results
print(f"Overall Score: {report.overall_score:.1f}%")
print(f"Missing Topics: {report.missing_topics}")
```

### Generate Reports
```python
from bcm_framework_validator import BCMReportGenerator

# Generate all reports
generator = BCMReportGenerator("output_folder")
files = generator.generate_all_reports(report)

print("Generated reports:", files.keys())
```

### Framework Analysis
```python
from bcm_framework_validator import BCMFrameworkLoader

# Load framework
loader = BCMFrameworkLoader("BCM_Plan_Framework.docx")
framework = loader.load_framework()

print(f"Total topics: {framework.total_topics}")
print(f"Categories: {list(framework.categories.keys())}")
```

## 🔧 Configuration Options

### Model Configuration
```python
# Use different Mistral model
validator = AIBCMValidator(
    mistral_path="path/to/your/model.gguf"
)

# Use different framework
validator = AIBCMValidator(
    framework_path="custom_framework.docx"
)
```

### Ollama Configuration
```python
# Custom Ollama settings
from bcm_framework_validator.ai_validator import OllamaEmbeddings

embeddings = OllamaEmbeddings(
    model_name="custom-embed-model",
    base_url="http://custom-ollama:11434"
)
```

## 📈 Sample Output

```
🎉 VALIDATION COMPLETED!
========================================
📄 Document: company_bcm_plan.pdf
📊 Overall Score: 73.2%
✅ Found: 55 topics
⚠️ Partial: 12 topics  
❌ Missing: 8 topics

🎯 Priority Actions:
1. Crisis Management Team: Define team structure and roles
2. Business Impact Analysis: Conduct comprehensive BIA
3. Communication Guidelines: Develop crisis communication protocols

💡 Quick Wins:
1. Contact Information: Update emergency contact details
2. Assembly Points: Clearly define evacuation points
3. Vendor Contacts: Complete vendor contact database
```

## 🛠️ Troubleshooting

### Common Issues

**Model Not Found**
```bash
# Download Mistral model
# From: https://huggingface.co/TheBloke/Mistral-7B-Instruct-v0.1-GGUF
```

**Ollama Connection Error**
```bash
# Start Ollama service
ollama serve

# Pull required models
ollama pull nomic-embed-text
ollama pull mistral
```

**Framework Loading Error**
```bash
# Install python-docx
pip install python-docx

# Check file path and permissions
```

### Performance Tips

1. **Use SSD storage** for model files
2. **Allocate sufficient RAM** (8GB+ recommended)
3. **Use GPU acceleration** if available with llama-cpp-python
4. **Chunk large documents** for better processing

## 🔄 Integration Options

### API Integration
```python
# Create REST API wrapper
from flask import Flask, request, jsonify

app = Flask(__name__)
validator = AIBCMValidator()

@app.route('/validate', methods=['POST'])
def validate_document():
    file = request.files['document']
    report = validator.validate_document(file)
    return jsonify(asdict(report))
```

### Batch Processing
```python
# Validate multiple documents
import os

for filename in os.listdir("bcm_documents/"):
    if filename.endswith('.pdf'):
        report = validator.validate_document(filename)
        print(f"{filename}: {report.overall_score:.1f}%")
```

## 📚 Advanced Features

### Custom Prompts
Modify validation prompts in `framework_loader.py`:
```python
def get_validation_prompts(self):
    return {
        "Custom Category": "Your custom validation prompt..."
    }
```

### Custom Scoring
Implement custom scoring logic in `ai_validator.py`:
```python
def calculate_custom_score(self, findings):
    # Your custom scoring algorithm
    pass
```

### Report Customization
Extend report generation in `report_generator.py`:
```python
def generate_custom_report(self, report, output_file):
    # Your custom report format
    pass
```

## 🤝 Support

For questions or issues:
1. Check the troubleshooting section
2. Run `python main.py --check-setup` for diagnostics
3. Review the generated logs
4. Ensure all dependencies are properly installed

## 📄 License

This BCM Framework Validator is designed for internal use in BCM compliance and improvement initiatives.

---

**🎯 Ready to validate your BCM documents with AI intelligence!**

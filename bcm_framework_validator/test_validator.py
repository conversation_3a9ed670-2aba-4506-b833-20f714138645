"""
Test script for BCM Framework Validator
Quick test to verify the system is working correctly.
"""

import os
import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_framework_loading():
    """Test framework loading"""
    print("🧪 Testing Framework Loading...")
    
    try:
        from framework_loader import BCMFrameworkLoader
        
        # Test with the framework document
        framework_path = "BCM_Plan_Framework.docx"
        if not os.path.exists(framework_path):
            framework_path = "../BCM_Plan_Framework.docx"
        
        if os.path.exists(framework_path):
            loader = BCMFrameworkLoader(framework_path)
            framework = loader.load_framework()
            
            print(f"✅ Framework loaded successfully!")
            print(f"   Total topics: {framework.total_topics}")
            print(f"   Categories: {len(framework.categories)}")
            
            # Show sample topics
            print(f"\n📋 Sample topics by category:")
            for category, topics in list(framework.categories.items())[:3]:
                print(f"   {category}: {len(topics)} topics")
                for topic in topics[:2]:
                    print(f"     • {topic.title}")
            
            return True
        else:
            print(f"❌ Framework document not found: {framework_path}")
            return False
            
    except Exception as e:
        print(f"❌ Framework loading failed: {e}")
        return False

def test_ai_models():
    """Test AI model availability"""
    print("\n🤖 Testing AI Models...")
    
    # Test Ollama connection
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running")
            models = response.json().get('models', [])
            model_names = [m['name'] for m in models]
            
            if any('nomic' in name for name in model_names):
                print("✅ Nomic embeddings available")
            else:
                print("⚠️ Nomic embeddings not found")
                
            if any('mistral' in name for name in model_names):
                print("✅ Mistral model available in Ollama")
            else:
                print("⚠️ Mistral model not found in Ollama")
        else:
            print("❌ Ollama not responding")
    except Exception as e:
        print(f"⚠️ Ollama connection failed: {e}")
    
    # Test Mistral model file
    model_paths = [
        "model/mistral-7b-instruct-v0.1.Q4_K_M.gguf",
        "../model/mistral-7b-instruct-v0.1.Q4_K_M.gguf"
    ]
    
    model_found = False
    for model_path in model_paths:
        if os.path.exists(model_path):
            size_gb = os.path.getsize(model_path) / (1024**3)
            print(f"✅ Mistral model found: {model_path} ({size_gb:.1f} GB)")
            model_found = True
            break
    
    if not model_found:
        print("⚠️ Mistral model file not found")
    
    return True

def test_dependencies():
    """Test Python dependencies"""
    print("\n📦 Testing Dependencies...")
    
    required_packages = [
        ('docx', 'python-docx'),
        ('requests', 'requests'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
    ]
    
    optional_packages = [
        ('llama_cpp', 'llama-cpp-python'),
        ('PyPDF2', 'PyPDF2'),
    ]
    
    all_good = True
    
    for module, package in required_packages:
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Install with: pip install {package}")
            all_good = False
    
    print("\nOptional packages:")
    for module, package in optional_packages:
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"⚠️ {package} - Install with: pip install {package}")
    
    return all_good

def test_sample_validation():
    """Test sample validation"""
    print("\n🔍 Testing Sample Validation...")
    
    try:
        from ai_validator import AIBCMValidator
        
        # Find framework document
        framework_path = "BCM_Plan_Framework.docx"
        if not os.path.exists(framework_path):
            framework_path = "../BCM_Plan_Framework.docx"
        
        if not os.path.exists(framework_path):
            print("❌ Framework document not found for testing")
            return False
        
        # Find a test document
        test_docs = [
            "Perpetuuiti_BCM_Plan.pdf",
            "../Perpetuuiti_BCM_Plan.pdf",
            "test_document.txt"
        ]
        
        test_doc = None
        for doc in test_docs:
            if os.path.exists(doc):
                test_doc = doc
                break
        
        if not test_doc:
            # Create a simple test document
            test_doc = "test_bcm_sample.txt"
            with open(test_doc, 'w') as f:
                f.write("""
Sample BCM Document

Policy Statement:
Our organization is committed to maintaining business continuity.

Crisis Management Team:
- John Smith (Crisis Manager)
- Jane Doe (Communications Lead)

Emergency Procedures:
In case of fire, evacuate immediately.

Communication Guidelines:
Notify all stakeholders within 2 hours.

Risk Assessment:
We have identified key business risks.
""")
            print(f"📝 Created test document: {test_doc}")
        
        # Initialize validator
        print("🔧 Initializing validator...")
        validator = AIBCMValidator(framework_path)
        
        # Load framework
        framework = validator.load_framework()
        print(f"✅ Framework loaded: {framework.total_topics} topics")
        
        # Test document text extraction
        if test_doc.endswith('.txt'):
            with open(test_doc, 'r') as f:
                doc_text = f.read()
            print(f"✅ Test document loaded: {len(doc_text)} characters")
        else:
            doc_text = validator._extract_document_text(test_doc)
            if doc_text:
                print(f"✅ Test document extracted: {len(doc_text)} characters")
            else:
                print("⚠️ Could not extract document text")
                return False
        
        print("🎯 Validation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Sample validation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 BCM Framework Validator - System Test")
    print("=" * 50)
    
    tests = [
        ("Framework Loading", test_framework_loading),
        ("Dependencies", test_dependencies),
        ("AI Models", test_ai_models),
        ("Sample Validation", test_sample_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("🎯 TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! System is ready for use.")
        print("\nNext steps:")
        print("1. Run: python main.py --check-setup")
        print("2. Try: python main.py --document 'your_bcm_document.pdf'")
    else:
        print("\n⚠️ Some tests failed. Please address the issues above.")
        print("\nCommon fixes:")
        print("1. Install missing dependencies: pip install python-docx requests matplotlib seaborn")
        print("2. Download Mistral model to model/ directory")
        print("3. Install and start Ollama with required models")
        print("4. Ensure BCM_Plan_Framework.docx is in the correct location")

if __name__ == "__main__":
    main()

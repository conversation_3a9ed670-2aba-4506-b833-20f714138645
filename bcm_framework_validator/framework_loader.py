"""
BCM Framework Loader
Loads and processes the BCM Plan Framework document to extract standard topics.
"""

import os
import docx
from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class FrameworkTopic:
    """Represents a single BCM framework topic"""
    id: int
    title: str
    category: str = ""
    description: str = ""
    is_mandatory: bool = True

@dataclass
class BCMFramework:
    """Complete BCM framework with all topics"""
    topics: List[FrameworkTopic]
    total_topics: int
    categories: Dict[str, List[FrameworkTopic]]
    
    def get_topic_by_id(self, topic_id: int) -> FrameworkTopic:
        """Get topic by ID"""
        for topic in self.topics:
            if topic.id == topic_id:
                return topic
        return None
    
    def get_topics_by_category(self, category: str) -> List[FrameworkTopic]:
        """Get all topics in a category"""
        return self.categories.get(category, [])

class BCMFrameworkLoader:
    """Loads BCM framework from the reference document"""
    
    def __init__(self, framework_path: str = "BCM_Plan_Framework.docx"):
        self.framework_path = framework_path
        self.framework = None
        
    def load_framework(self) -> BCMFramework:
        """Load the BCM framework from the document"""
        
        if not os.path.exists(self.framework_path):
            raise FileNotFoundError(f"Framework document not found: {self.framework_path}")
        
        print(f"📋 Loading BCM framework from: {self.framework_path}")
        
        try:
            # Load Word document
            doc = docx.Document(self.framework_path)
            topics = []
            
            # Extract topics from document
            for i, para in enumerate(doc.paragraphs, 1):
                text = para.text.strip()
                if text and not text.startswith("Revision") and len(text) > 3:
                    # Categorize topics
                    category = self._categorize_topic(text)
                    
                    topic = FrameworkTopic(
                        id=i,
                        title=text,
                        category=category,
                        description=self._get_topic_description(text),
                        is_mandatory=self._is_mandatory_topic(text)
                    )
                    topics.append(topic)
            
            # Group by categories
            categories = {}
            for topic in topics:
                if topic.category not in categories:
                    categories[topic.category] = []
                categories[topic.category].append(topic)
            
            self.framework = BCMFramework(
                topics=topics,
                total_topics=len(topics),
                categories=categories
            )
            
            print(f"✅ Loaded {len(topics)} framework topics in {len(categories)} categories")
            return self.framework
            
        except Exception as e:
            raise Exception(f"Error loading framework: {e}")
    
    def _categorize_topic(self, topic_title: str) -> str:
        """Categorize a topic based on its title"""
        
        title_lower = topic_title.lower()
        
        # Define category mappings
        if any(word in title_lower for word in ['policy', 'statement', 'objectives', 'scope']):
            return "Policy & Governance"
        elif any(word in title_lower for word in ['personnel', 'contact', 'team', 'roles', 'responsibilities']):
            return "Personnel & Organization"
        elif any(word in title_lower for word in ['crisis', 'emergency', 'fire', 'power', 'failure', 'intrusion', 'hacking']):
            return "Crisis Management"
        elif any(word in title_lower for word in ['communication', 'media', 'notification']):
            return "Communication"
        elif any(word in title_lower for word in ['recovery', 'normalcy', 'review', 'testing']):
            return "Recovery & Testing"
        elif any(word in title_lower for word in ['risk', 'impact', 'analysis', 'assessment']):
            return "Risk & Impact Analysis"
        elif any(word in title_lower for word in ['appendix', 'forms', 'template']):
            return "Documentation & Forms"
        elif any(word in title_lower for word in ['training', 'awareness', 'exercise']):
            return "Training & Exercises"
        elif any(word in title_lower for word in ['legal', 'insurance', 'vendor']):
            return "Legal & Compliance"
        else:
            return "General"
    
    def _get_topic_description(self, topic_title: str) -> str:
        """Get description for a topic"""
        
        descriptions = {
            "Policy Statement": "Defines the organization's commitment to business continuity",
            "Objectives": "Specific goals and targets for the BCM program",
            "Scope": "What is covered by the BCM plan",
            "Crisis Management Team": "Team responsible for managing crisis situations",
            "Roles and responsibilities": "Clear definition of who does what during incidents",
            "Business Impact Analysis": "Assessment of potential impacts from disruptions",
            "Risk Management": "Identification and mitigation of business risks",
            "Communication Guidelines": "How to communicate during and after incidents",
            "Recovery": "Steps to restore normal operations",
            "Testing": "Regular testing and validation of the plan"
        }
        
        # Find best match
        for key, desc in descriptions.items():
            if key.lower() in topic_title.lower():
                return desc
        
        return f"Important BCM component: {topic_title}"
    
    def _is_mandatory_topic(self, topic_title: str) -> bool:
        """Determine if a topic is mandatory"""
        
        # Most topics are mandatory, but some appendices might be optional
        optional_keywords = ['appendix', 'sample', 'template', 'form']
        return not any(keyword in topic_title.lower() for keyword in optional_keywords)
    
    def get_framework_summary(self) -> str:
        """Get a summary of the framework"""
        
        if not self.framework:
            return "Framework not loaded"
        
        summary = f"""
BCM Framework Summary:
=====================
Total Topics: {self.framework.total_topics}
Categories: {len(self.framework.categories)}

Category Breakdown:
"""
        
        for category, topics in self.framework.categories.items():
            mandatory_count = sum(1 for t in topics if t.is_mandatory)
            summary += f"  • {category}: {len(topics)} topics ({mandatory_count} mandatory)\n"
        
        return summary
    
    def get_validation_prompts(self) -> Dict[str, str]:
        """Get AI prompts for validating each category"""
        
        prompts = {
            "Policy & Governance": """
Analyze if the document contains clear policy statements, objectives, scope definition, and governance structure. 
Look for: organizational commitment, clear objectives, defined scope, exclusions, and assumptions.
""",
            "Personnel & Organization": """
Analyze if the document defines key personnel, contact information, team structure, and roles/responsibilities.
Look for: crisis management team, contact details, organizational chart, clear role definitions.
""",
            "Crisis Management": """
Analyze if the document covers various crisis scenarios and response procedures.
Look for: emergency procedures, incident types (fire, power failure, security breaches), response protocols.
""",
            "Communication": """
Analyze if the document has comprehensive communication guidelines for internal and external stakeholders.
Look for: communication protocols, media handling, stakeholder notification, crisis communication templates.
""",
            "Recovery & Testing": """
Analyze if the document covers recovery procedures, return to normalcy, and regular testing.
Look for: recovery steps, business resumption, testing schedules, post-incident reviews.
""",
            "Risk & Impact Analysis": """
Analyze if the document includes proper risk assessment and business impact analysis.
Look for: risk identification, impact assessment, business impact analysis, critical processes.
""",
            "Documentation & Forms": """
Analyze if the document includes necessary forms, templates, and documentation requirements.
Look for: incident forms, contact lists, templates, documentation standards.
""",
            "Training & Exercises": """
Analyze if the document covers training requirements and exercise programs.
Look for: training programs, awareness activities, exercise schedules, competency requirements.
""",
            "Legal & Compliance": """
Analyze if the document addresses legal requirements, insurance, and vendor management.
Look for: legal compliance, insurance coverage, vendor agreements, regulatory requirements.
"""
        }
        
        return prompts

if __name__ == "__main__":
    # Test the framework loader
    loader = BCMFrameworkLoader()
    framework = loader.load_framework()
    print(loader.get_framework_summary())

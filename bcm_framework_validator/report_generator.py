"""
BCM Validation Report Generator
Generates comprehensive, user-friendly reports from validation results.
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    print("⚠️ Matplotlib/Seaborn not available - charts will be skipped")
from dataclasses import asdict

from ai_validator import ValidationReport, ValidationFinding

class BCMReportGenerator:
    """Generates various types of validation reports"""

    def __init__(self, output_dir: str = "validation_reports"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Set up plotting style if available
        if PLOTTING_AVAILABLE:
            plt.style.use('default')
            sns.set_palette("husl")

    def generate_all_reports(self, report: ValidationReport) -> Dict[str, str]:
        """Generate all types of reports"""

        timestamp = report.validation_date.strftime('%Y%m%d_%H%M%S')
        doc_name = report.document_name.replace('.pdf', '').replace('.docx', '')

        generated_files = {}

        # 1. Executive Summary (Simple text)
        exec_file = f"{self.output_dir}/executive_summary_{doc_name}_{timestamp}.txt"
        self.generate_executive_summary(report, exec_file)
        generated_files['executive_summary'] = exec_file

        # 2. Detailed Analysis Report
        detail_file = f"{self.output_dir}/detailed_analysis_{doc_name}_{timestamp}.txt"
        self.generate_detailed_report(report, detail_file)
        generated_files['detailed_analysis'] = detail_file

        # 3. Action Plan (What to do next)
        action_file = f"{self.output_dir}/action_plan_{doc_name}_{timestamp}.txt"
        self.generate_action_plan(report, action_file)
        generated_files['action_plan'] = action_file

        # 4. Gap Analysis Report
        gap_file = f"{self.output_dir}/gap_analysis_{doc_name}_{timestamp}.txt"
        self.generate_gap_analysis(report, gap_file)
        generated_files['gap_analysis'] = gap_file

        # 5. Compliance Matrix (CSV)
        matrix_file = f"{self.output_dir}/compliance_matrix_{doc_name}_{timestamp}.csv"
        self.generate_compliance_matrix(report, matrix_file)
        generated_files['compliance_matrix'] = matrix_file

        # 6. Visual Charts
        if PLOTTING_AVAILABLE:
            try:
                chart_file = f"{self.output_dir}/compliance_charts_{doc_name}_{timestamp}.png"
                self.generate_visual_charts(report, chart_file)
                generated_files['charts'] = chart_file
            except Exception as e:
                print(f"⚠️ Could not generate charts: {e}")
        else:
            print("⚠️ Skipping charts - matplotlib/seaborn not available")

        # 7. JSON Export (for integration)
        json_file = f"{self.output_dir}/validation_data_{doc_name}_{timestamp}.json"
        self.generate_json_export(report, json_file)
        generated_files['json_export'] = json_file

        print(f"📊 Generated {len(generated_files)} report files in: {self.output_dir}")
        return generated_files

    def generate_executive_summary(self, report: ValidationReport, output_file: str):
        """Generate executive summary for management"""

        content = f"""
BCM VALIDATION EXECUTIVE SUMMARY
===============================

Document: {report.document_name}
Validation Date: {report.validation_date.strftime('%B %d, %Y at %I:%M %p')}
Overall Compliance Score: {report.overall_score:.1f}%

{report.executive_summary}

COMPLIANCE BREAKDOWN:
====================
✅ Fully Covered Topics: {report.found_topics} ({report.found_topics/report.total_topics*100:.1f}%)
⚠️ Partially Covered Topics: {report.partial_topics} ({report.partial_topics/report.total_topics*100:.1f}%)
❌ Missing Topics: {report.missing_topics} ({report.missing_topics/report.total_topics*100:.1f}%)

CATEGORY PERFORMANCE:
====================
"""

        # Sort categories by score
        sorted_categories = sorted(report.category_scores.items(), key=lambda x: x[1], reverse=True)

        for category, score in sorted_categories:
            status_icon = "✅" if score >= 80 else "⚠️" if score >= 60 else "❌"
            content += f"{status_icon} {category}: {score:.1f}%\n"

        content += f"""

IMMEDIATE PRIORITIES:
====================
"""

        for i, gap in enumerate(report.priority_gaps[:3], 1):
            content += f"{i}. {gap}\n"

        content += f"""

QUICK WINS (Easy Improvements):
==============================
"""

        for i, win in enumerate(report.quick_wins[:3], 1):
            content += f"{i}. {win}\n"

        content += f"""

RECOMMENDATION:
==============
"""

        if report.overall_score >= 80:
            content += "Your BCM plan is in good shape! Focus on the quick wins to achieve excellence."
        elif report.overall_score >= 60:
            content += "Your BCM plan has a solid foundation. Address the priority gaps to improve compliance."
        else:
            content += "Your BCM plan needs significant improvement. Start with the immediate priorities."

        content += f"""

Next Steps:
1. Review the detailed analysis report
2. Assign owners for each priority gap
3. Set target dates for improvements
4. Schedule regular reviews

For detailed findings, see the full analysis report.
"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"📄 Executive summary saved: {output_file}")

    def generate_detailed_report(self, report: ValidationReport, output_file: str):
        """Generate detailed analysis report"""

        content = f"""
BCM VALIDATION DETAILED ANALYSIS
================================

Document: {report.document_name}
Validation Date: {report.validation_date.strftime('%B %d, %Y at %I:%M %p')}
Framework Version: {report.framework_version}

OVERALL RESULTS:
===============
Overall Score: {report.overall_score:.1f}%
Total Topics Analyzed: {report.total_topics}
Found: {report.found_topics} | Partial: {report.partial_topics} | Missing: {report.missing_topics}

DETAILED FINDINGS BY CATEGORY:
=============================

"""

        # Group findings by category
        by_category = {}
        for finding in report.findings:
            if finding.category not in by_category:
                by_category[finding.category] = []
            by_category[finding.category].append(finding)

        # Generate detailed analysis for each category
        for category, findings in by_category.items():
            category_score = report.category_scores.get(category, 0)

            content += f"""
{category.upper()}
{'-' * len(category)}
Category Score: {category_score:.1f}%

"""

            for finding in findings:
                status_icon = {
                    "FOUND": "✅",
                    "PARTIAL": "⚠️",
                    "MISSING": "❌",
                    "UNCLEAR": "❓"
                }.get(finding.status, "❓")

                content += f"""
{status_icon} {finding.topic_title}
   Status: {finding.status} (Confidence: {finding.confidence:.1f})
   Evidence: {finding.evidence[:200]}{'...' if len(finding.evidence) > 200 else ''}
"""

                if finding.gaps:
                    content += f"   Gaps: {', '.join(finding.gaps[:2])}\n"

                if finding.recommendations:
                    content += f"   Recommendation: {finding.recommendations[0]}\n"

                content += "\n"

        content += f"""

SUMMARY RECOMMENDATIONS:
=======================

HIGH PRIORITY (Address First):
------------------------------
"""

        for i, gap in enumerate(report.priority_gaps, 1):
            content += f"{i}. {gap}\n"

        content += f"""

QUICK WINS (Easy to Implement):
-------------------------------
"""

        for i, win in enumerate(report.quick_wins, 1):
            content += f"{i}. {win}\n"

        content += f"""

VALIDATION METHODOLOGY:
======================
This validation was performed using AI analysis with the following approach:
1. Document text extraction and preprocessing
2. Semantic analysis using Ollama embeddings
3. Content analysis using Mistral 7B language model
4. Framework mapping against {report.total_topics} standard BCM topics
5. Confidence scoring and evidence extraction
6. Gap identification and recommendation generation

For questions about this analysis, please review the methodology documentation.
"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"📄 Detailed analysis saved: {output_file}")

    def generate_action_plan(self, report: ValidationReport, output_file: str):
        """Generate actionable improvement plan"""

        content = f"""
BCM IMPROVEMENT ACTION PLAN
==========================

Document: {report.document_name}
Generated: {datetime.now().strftime('%B %d, %Y')}
Current Score: {report.overall_score:.1f}%
Target Score: 90%+

PHASE 1: IMMEDIATE ACTIONS (Next 30 Days)
=========================================

Priority 1 - Critical Gaps:
"""

        for i, gap in enumerate(report.priority_gaps[:3], 1):
            content += f"""
{i}. {gap}
   Owner: [ TO BE ASSIGNED ]
   Due Date: [ SET TARGET DATE ]
   Resources Needed: [ IDENTIFY REQUIREMENTS ]
   Success Criteria: [ DEFINE COMPLETION CRITERIA ]

"""

        content += f"""
PHASE 2: QUICK WINS (Next 60 Days)
==================================

Easy Improvements:
"""

        for i, win in enumerate(report.quick_wins[:3], 1):
            content += f"""
{i}. {win}
   Owner: [ TO BE ASSIGNED ]
   Due Date: [ SET TARGET DATE ]
   Effort Level: Low
   Impact: Medium

"""

        content += f"""
PHASE 3: COMPREHENSIVE IMPROVEMENTS (Next 90 Days)
==================================================

Remaining Gaps to Address:
"""

        # Find remaining missing topics
        missing_findings = [f for f in report.findings if f.status == "MISSING"]
        for i, finding in enumerate(missing_findings[:5], 1):
            content += f"""
{i}. {finding.topic_title}
   Category: {finding.category}
   Action Required: {finding.recommendations[0] if finding.recommendations else 'Develop and document'}
   Owner: [ TO BE ASSIGNED ]
   Due Date: [ SET TARGET DATE ]

"""

        content += f"""
IMPLEMENTATION CHECKLIST:
========================

Week 1:
□ Assign owners for each action item
□ Set specific target dates
□ Identify required resources
□ Communicate plan to stakeholders

Week 2-4:
□ Begin work on Priority 1 items
□ Schedule regular progress reviews
□ Document progress and challenges
□ Adjust timeline if needed

Month 2:
□ Complete Priority 1 items
□ Begin Quick Wins implementation
□ Conduct interim validation
□ Update documentation

Month 3:
□ Complete Quick Wins
□ Begin comprehensive improvements
□ Prepare for final validation
□ Plan ongoing maintenance

TRACKING AND MONITORING:
=======================

Progress Metrics:
- Number of gaps closed
- Compliance score improvement
- Documentation completeness
- Team readiness level

Review Schedule:
- Weekly progress check-ins
- Monthly compliance assessment
- Quarterly full validation
- Annual plan review and update

SUCCESS CRITERIA:
================
□ Overall compliance score > 90%
□ All critical gaps addressed
□ Documentation complete and current
□ Team trained and ready
□ Regular testing schedule established

BUDGET CONSIDERATIONS:
=====================
- Training costs: [ ESTIMATE ]
- Documentation development: [ ESTIMATE ]
- External consulting: [ ESTIMATE ]
- Technology/tools: [ ESTIMATE ]
- Testing and exercises: [ ESTIMATE ]

Total Estimated Budget: [ CALCULATE TOTAL ]

RISK MITIGATION:
===============
- Resource availability: [ PLAN FOR BACKUP RESOURCES ]
- Timeline delays: [ BUILD IN BUFFER TIME ]
- Stakeholder buy-in: [ ENSURE EXECUTIVE SUPPORT ]
- Technical challenges: [ IDENTIFY EXPERT SUPPORT ]

This action plan should be reviewed and updated monthly to ensure progress toward BCM excellence.
"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"📋 Action plan saved: {output_file}")

    def generate_gap_analysis(self, report: ValidationReport, output_file: str):
        """Generate detailed gap analysis"""

        content = f"""
BCM GAP ANALYSIS REPORT
======================

Document: {report.document_name}
Analysis Date: {report.validation_date.strftime('%B %d, %Y')}

EXECUTIVE SUMMARY:
=================
{report.executive_summary}

DETAILED GAP ANALYSIS:
=====================

"""

        # Analyze gaps by category
        by_category = {}
        for finding in report.findings:
            if finding.category not in by_category:
                by_category[finding.category] = []
            by_category[finding.category].append(finding)

        for category, findings in by_category.items():
            missing_count = sum(1 for f in findings if f.status == "MISSING")
            partial_count = sum(1 for f in findings if f.status == "PARTIAL")
            total_count = len(findings)

            content += f"""
{category}:
{'-' * (len(category) + 1)}
Total Topics: {total_count}
Missing: {missing_count}
Partial: {partial_count}
Gap Severity: {'HIGH' if missing_count > total_count/2 else 'MEDIUM' if missing_count > 0 else 'LOW'}

Critical Missing Elements:
"""

            for finding in findings:
                if finding.status == "MISSING":
                    content += f"  • {finding.topic_title}\n"
                    if finding.gaps:
                        content += f"    Gaps: {', '.join(finding.gaps)}\n"

            content += "\nPartial Coverage Issues:\n"
            for finding in findings:
                if finding.status == "PARTIAL":
                    content += f"  • {finding.topic_title}: {finding.gaps[0] if finding.gaps else 'Incomplete coverage'}\n"

            content += "\n"

        content += f"""
IMPACT ASSESSMENT:
=================

Business Risk Level: {'HIGH' if report.overall_score < 60 else 'MEDIUM' if report.overall_score < 80 else 'LOW'}

Potential Impacts of Current Gaps:
• Regulatory compliance issues
• Inadequate crisis response
• Extended recovery times
• Stakeholder confidence loss
• Financial and operational losses

REMEDIATION PRIORITIES:
======================

1. CRITICAL (Address Immediately):
"""

        critical_findings = [f for f in report.findings if f.status == "MISSING" and
                           any(critical in f.topic_title.lower() for critical in
                               ['crisis', 'team', 'communication', 'risk', 'impact'])]

        for finding in critical_findings[:5]:
            content += f"   • {finding.topic_title}\n"

        content += f"""

2. HIGH (Address within 30 days):
"""

        high_findings = [f for f in report.findings if f.status == "MISSING" and f not in critical_findings]
        for finding in high_findings[:5]:
            content += f"   • {finding.topic_title}\n"

        content += f"""

3. MEDIUM (Address within 60 days):
"""

        medium_findings = [f for f in report.findings if f.status == "PARTIAL"]
        for finding in medium_findings[:5]:
            content += f"   • {finding.topic_title}\n"

        content += f"""

RESOURCE REQUIREMENTS:
=====================

Estimated Effort by Category:
"""

        for category, score in report.category_scores.items():
            effort = "HIGH" if score < 50 else "MEDIUM" if score < 80 else "LOW"
            content += f"• {category}: {effort} effort required\n"

        content += f"""

COMPLIANCE ROADMAP:
==================

Current State: {report.overall_score:.1f}% compliant
Target State: 90%+ compliant

Milestones:
• Month 1: Address critical gaps → Target: 70%
• Month 2: Complete high priority items → Target: 80%
• Month 3: Finish medium priority items → Target: 90%+

This gap analysis should be used to prioritize improvement efforts and allocate resources effectively.
"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"📊 Gap analysis saved: {output_file}")

    def generate_compliance_matrix(self, report: ValidationReport, output_file: str):
        """Generate compliance matrix in CSV format"""

        import csv

        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Header
            writer.writerow([
                'Topic ID', 'Topic Title', 'Category', 'Status', 'Confidence',
                'Evidence Found', 'Primary Gap', 'Primary Recommendation'
            ])

            # Data rows
            for finding in report.findings:
                writer.writerow([
                    finding.topic_id,
                    finding.topic_title,
                    finding.category,
                    finding.status,
                    f"{finding.confidence:.2f}",
                    finding.evidence[:100] + "..." if len(finding.evidence) > 100 else finding.evidence,
                    finding.gaps[0] if finding.gaps else "",
                    finding.recommendations[0] if finding.recommendations else ""
                ])

        print(f"📊 Compliance matrix saved: {output_file}")

    def generate_visual_charts(self, report: ValidationReport, output_file: str):
        """Generate visual charts"""

        if not PLOTTING_AVAILABLE:
            print("⚠️ Plotting libraries not available")
            return

        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'BCM Compliance Analysis - {report.document_name}', fontsize=16, fontweight='bold')

            # 1. Overall Score Gauge
            ax1.pie([report.overall_score, 100-report.overall_score],
                   labels=['Compliant', 'Non-Compliant'],
                   colors=['#2ecc71', '#e74c3c'],
                   startangle=90,
                   counterclock=False)
            ax1.set_title(f'Overall Compliance: {report.overall_score:.1f}%', fontweight='bold')

            # 2. Status Distribution
            status_counts = {'Found': report.found_topics, 'Partial': report.partial_topics, 'Missing': report.missing_topics}
            ax2.bar(status_counts.keys(), status_counts.values(),
                   color=['#2ecc71', '#f39c12', '#e74c3c'])
            ax2.set_title('Topic Status Distribution', fontweight='bold')
            ax2.set_ylabel('Number of Topics')

            # 3. Category Scores
            categories = list(report.category_scores.keys())
            scores = list(report.category_scores.values())

            bars = ax3.barh(categories, scores, color=['#2ecc71' if s >= 80 else '#f39c12' if s >= 60 else '#e74c3c' for s in scores])
            ax3.set_title('Compliance by Category', fontweight='bold')
            ax3.set_xlabel('Compliance Score (%)')
            ax3.set_xlim(0, 100)

            # Add score labels on bars
            for i, (bar, score) in enumerate(zip(bars, scores)):
                ax3.text(score + 1, i, f'{score:.1f}%', va='center')

            # 4. Improvement Trend (simulated)
            months = ['Current', 'Month 1', 'Month 2', 'Month 3']
            projected_scores = [
                report.overall_score,
                min(report.overall_score + 15, 100),
                min(report.overall_score + 25, 100),
                min(report.overall_score + 35, 100)
            ]

            ax4.plot(months, projected_scores, marker='o', linewidth=3, markersize=8, color='#3498db')
            ax4.fill_between(months, projected_scores, alpha=0.3, color='#3498db')
            ax4.set_title('Projected Improvement Timeline', fontweight='bold')
            ax4.set_ylabel('Compliance Score (%)')
            ax4.set_ylim(0, 100)
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"📈 Visual charts saved: {output_file}")

        except Exception as e:
            print(f"⚠️ Error generating charts: {e}")

    def generate_json_export(self, report: ValidationReport, output_file: str):
        """Generate JSON export for integration"""

        # Convert report to dictionary
        report_dict = asdict(report)

        # Convert datetime to string
        report_dict['validation_date'] = report.validation_date.isoformat()

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)

        print(f"💾 JSON export saved: {output_file}")

if __name__ == "__main__":
    # Test report generation
    print("Report generator ready for use!")

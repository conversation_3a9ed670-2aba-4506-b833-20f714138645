"""
Test ISO Standards functionality without requiring Ollama
"""

from bcm_expert_chatbot.iso_standards import ISO22301Standards

def test_iso_standards():
    """Test ISO 22301 standards functionality"""
    print("🤖 BCM Expert Chatbot - ISO Standards Test")
    print("=" * 60)
    
    try:
        # Initialize ISO standards
        print("📋 Initializing ISO 22301 standards...")
        iso_standards = ISO22301Standards()
        
        # Get all requirements
        all_requirements = iso_standards.get_all_requirements()
        print(f"✅ Loaded {len(all_requirements)} ISO 22301 requirements")
        
        # Show requirements by category
        print(f"\\n📂 Requirements by category:")
        by_category = iso_standards.get_requirements_by_category()
        for category, reqs in by_category.items():
            print(f"   • {category}: {len(reqs)} requirements")
        
        # Show critical requirements
        critical = iso_standards.get_critical_requirements()
        print(f"\\n🔥 Critical requirements ({len(critical)}):")
        for req in critical:
            print(f"   • {req.clause}: {req.title}")
        
        # Show detailed example
        print(f"\\n📋 Example Requirement Details (8.2 - Business Impact Analysis):")
        req_8_2 = iso_standards.get_requirement("8.2")
        if req_8_2:
            print(f"   Title: {req_8_2.title}")
            print(f"   Description: {req_8_2.description}")
            print(f"   Key Elements ({len(req_8_2.key_elements)}):")
            for element in req_8_2.key_elements:
                print(f"     - {element}")
            print(f"   Validation Criteria ({len(req_8_2.validation_criteria)}):")
            for criteria in req_8_2.validation_criteria:
                print(f"     - {criteria}")
            print(f"   Common Gaps ({len(req_8_2.common_gaps)}):")
            for gap in req_8_2.common_gaps:
                print(f"     - {gap}")
        
        print(f"\\n✅ ISO Standards test completed successfully!")
        print(f"\\n💡 To use the full BCM validation system:")
        print(f"   1. Start Ollama service")
        print(f"   2. Ensure mistral and nomic-embed-text models are available")
        print(f"   3. Run: python -m bcm_expert_chatbot.main chat")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_iso_standards()

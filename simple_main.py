#!/usr/bin/env python3
"""
Simple BCM Validator - Works without Ollama
Basic BCM validation that works with just your topic list and basic analysis.
"""

import os
import sys
from datetime import datetime

def print_banner():
    """Print application banner"""
    print("""
🤖 ===============================================
   BCM EXPERT CHATBOT
   Simple BCM Document Validator
   ISO 22301 Basic Validation
===============================================
""")

def extract_text_from_pdf(file_path):
    """Extract text from PDF file"""
    try:
        import PyPDF2
        
        text = ""
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            
            for page_num, page in enumerate(reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
                except Exception as e:
                    print(f"Warning: Error extracting text from page {page_num + 1}: {e}")
        
        return text, len(reader.pages)
        
    except ImportError:
        print("❌ PyPDF2 not available. Install with: pip install PyPDF2")
        return None, 0
    except Exception as e:
        print(f"❌ Error reading PDF: {e}")
        return None, 0

def get_iso_requirements():
    """Get basic ISO 22301 requirements"""
    return {
        "4.1": "Understanding the organization and its context",
        "4.2": "Understanding the needs and expectations of interested parties", 
        "4.3": "Determining the scope of the business continuity management system",
        "4.4": "Business continuity management system",
        "5.1": "Leadership and commitment",
        "5.2": "Policy",
        "5.3": "Organizational roles, responsibilities and authorities",
        "6.1": "Actions to address risks and opportunities",
        "6.2": "Business continuity objectives and planning to achieve them",
        "7.1": "Resources",
        "7.2": "Competence", 
        "7.3": "Awareness",
        "7.4": "Communication",
        "7.5": "Documented information",
        "8.1": "Operational planning and control",
        "8.2": "Business impact analysis and risk assessment",
        "8.3": "Business continuity strategy",
        "8.4": "Business continuity procedures",
        "8.5": "Exercise programme",
        "9.1": "Monitoring, measurement, analysis and evaluation",
        "9.2": "Internal audit",
        "9.3": "Management review",
        "10.1": "Nonconformity and corrective action",
        "10.2": "Continual improvement"
    }

def get_requirement_keywords():
    """Get keywords for each requirement"""
    return {
        "4.1": ["context", "organization", "internal", "external", "environment"],
        "4.2": ["stakeholder", "interested parties", "requirements", "expectations"],
        "4.3": ["scope", "boundary", "applicability", "exclusion"],
        "4.4": ["bcms", "management system", "processes", "procedures"],
        "5.1": ["leadership", "commitment", "top management", "responsibility"],
        "5.2": ["policy", "statement", "commitment", "framework"],
        "5.3": ["roles", "responsibilities", "authorities", "organization"],
        "6.1": ["risk", "opportunities", "actions", "planning"],
        "6.2": ["objectives", "targets", "planning", "achievement"],
        "7.1": ["resources", "personnel", "infrastructure", "facilities"],
        "7.2": ["competence", "training", "skills", "qualification"],
        "7.3": ["awareness", "understanding", "communication"],
        "7.4": ["communication", "internal", "external", "information"],
        "7.5": ["documentation", "documented information", "records", "control"],
        "8.1": ["operational", "planning", "control", "implementation"],
        "8.2": ["business impact analysis", "bia", "risk assessment", "impact"],
        "8.3": ["strategy", "continuity strategy", "recovery", "approach"],
        "8.4": ["procedures", "response procedures", "recovery procedures"],
        "8.5": ["exercise", "testing", "drill", "validation"],
        "9.1": ["monitoring", "measurement", "analysis", "evaluation"],
        "9.2": ["internal audit", "audit", "auditing", "review"],
        "9.3": ["management review", "review", "evaluation", "improvement"],
        "10.1": ["nonconformity", "corrective action", "incident", "deviation"],
        "10.2": ["continual improvement", "improvement", "enhancement"]
    }

def analyze_requirement(text, clause, title, keywords):
    """Analyze if requirement is met using keyword matching"""
    text_lower = text.lower()
    
    # Count keyword matches
    matches = 0
    found_keywords = []
    
    for keyword in keywords:
        if keyword.lower() in text_lower:
            matches += 1
            found_keywords.append(keyword)
    
    # Determine status based on matches
    if matches >= 3:
        status = "Present and Adequate"
        confidence = min(0.9, 0.5 + (matches * 0.1))
    elif matches >= 1:
        status = "Present but Inadequate" 
        confidence = 0.3 + (matches * 0.1)
    else:
        status = "Missing"
        confidence = 0.8
    
    return {
        'clause': clause,
        'title': title,
        'status': status,
        'confidence': confidence,
        'matches': matches,
        'found_keywords': found_keywords,
        'missing_keywords': [k for k in keywords if k.lower() not in text_lower]
    }

def validate_bcm_document(file_path):
    """Validate BCM document using basic keyword analysis"""
    
    print(f"🔍 Starting BCM validation for: {os.path.basename(file_path)}")
    print("=" * 60)
    
    # Extract text from document
    if file_path.lower().endswith('.pdf'):
        print("📄 Extracting text from PDF...")
        text, pages = extract_text_from_pdf(file_path)
    else:
        print("❌ Only PDF files are supported in this simple version")
        return None
    
    if not text:
        print("❌ Could not extract text from document")
        return None
    
    print(f"✅ Extracted text from {pages} pages")
    print(f"📊 Document contains {len(text.split())} words")
    
    # Get requirements and keywords
    requirements = get_iso_requirements()
    keywords_map = get_requirement_keywords()
    
    print(f"\n🔍 Analyzing against {len(requirements)} ISO 22301 requirements...")
    print("=" * 60)
    
    # Analyze each requirement
    results = []
    status_counts = {"Present and Adequate": 0, "Present but Inadequate": 0, "Missing": 0}
    
    for clause, title in requirements.items():
        keywords = keywords_map.get(clause, [])
        result = analyze_requirement(text, clause, title, keywords)
        results.append(result)
        status_counts[result['status']] += 1
        
        # Print progress
        status_emoji = {
            "Present and Adequate": "✅",
            "Present but Inadequate": "⚠️",
            "Missing": "❌"
        }
        print(f"{status_emoji[result['status']]} {clause}: {title}")
        print(f"   Status: {result['status']} (Confidence: {result['confidence']:.2f})")
        print(f"   Found keywords: {', '.join(result['found_keywords'][:3]) if result['found_keywords'] else 'None'}")
        print()
    
    # Calculate overall compliance
    total = len(results)
    compliance_score = (status_counts["Present and Adequate"] + 0.5 * status_counts["Present but Inadequate"]) / total
    
    # Print summary
    print("=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    print(f"Overall Compliance Score: {compliance_score:.1%}")
    print(f"\n📈 Status Breakdown:")
    print(f"   ✅ Present and Adequate: {status_counts['Present and Adequate']} ({status_counts['Present and Adequate']/total*100:.1f}%)")
    print(f"   ⚠️ Present but Inadequate: {status_counts['Present but Inadequate']} ({status_counts['Present but Inadequate']/total*100:.1f}%)")
    print(f"   ❌ Missing: {status_counts['Missing']} ({status_counts['Missing']/total*100:.1f}%)")
    
    # Show critical gaps
    missing_requirements = [r for r in results if r['status'] == 'Missing']
    if missing_requirements:
        print(f"\n🚨 CRITICAL GAPS (Missing Requirements):")
        for req in missing_requirements:
            print(f"   • {req['clause']}: {req['title']}")
    
    # Show areas needing improvement
    inadequate_requirements = [r for r in results if r['status'] == 'Present but Inadequate']
    if inadequate_requirements:
        print(f"\n⚠️ AREAS NEEDING IMPROVEMENT:")
        for req in inadequate_requirements:
            print(f"   • {req['clause']}: {req['title']}")
            if req['missing_keywords']:
                print(f"     Missing keywords: {', '.join(req['missing_keywords'][:3])}")
    
    # Generate simple report
    print(f"\n📄 Generating simple report...")
    
    report_content = f"""# BCM Validation Report

**Document**: {os.path.basename(file_path)}
**Validation Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Overall Compliance**: {compliance_score:.1%}

## Summary

- **Total Requirements**: {total}
- **Present and Adequate**: {status_counts['Present and Adequate']} ({status_counts['Present and Adequate']/total*100:.1f}%)
- **Present but Inadequate**: {status_counts['Present but Inadequate']} ({status_counts['Present but Inadequate']/total*100:.1f}%)
- **Missing**: {status_counts['Missing']} ({status_counts['Missing']/total*100:.1f}%)

## Critical Gaps

"""
    
    for req in missing_requirements:
        report_content += f"- **{req['clause']}**: {req['title']}\n"
    
    report_content += "\n## Areas Needing Improvement\n\n"
    
    for req in inadequate_requirements:
        report_content += f"- **{req['clause']}**: {req['title']}\n"
    
    # Save report
    report_file = f"bcm_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ Report saved: {report_file}")
    
    return {
        'compliance_score': compliance_score,
        'status_counts': status_counts,
        'results': results,
        'missing_requirements': missing_requirements,
        'inadequate_requirements': inadequate_requirements
    }

def main():
    """Main function"""
    print_banner()
    
    # Get file path
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        print(f"📄 Using provided file: {file_path}")
    else:
        # Auto-detect BCM PDF in current directory
        bcm_files = []
        for file in os.listdir('.'):
            if file.lower().endswith('.pdf'):
                bcm_files.append(file)
        
        if bcm_files:
            print(f"📄 Found PDF documents in current directory:")
            for i, file in enumerate(bcm_files, 1):
                print(f"   {i}. {file}")
            
            if len(bcm_files) == 1:
                file_path = bcm_files[0]
                print(f"\n📄 Auto-selecting: {file_path}")
            else:
                print(f"\nSelect a file (1-{len(bcm_files)}):")
                try:
                    choice = int(input("Choice: ").strip())
                    if 1 <= choice <= len(bcm_files):
                        file_path = bcm_files[choice - 1]
                    else:
                        print("❌ Invalid choice")
                        return
                except ValueError:
                    print("❌ Invalid input")
                    return
        else:
            print("📄 No PDF documents found in current directory.")
            file_path = input("Enter PDF file path: ").strip().strip('"').strip("'")
    
    # Validate input
    if not file_path:
        print("❌ No file path provided")
        return
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    if not file_path.lower().endswith('.pdf'):
        print("❌ Only PDF files are supported in this simple version")
        return
    
    # Process the document
    print(f"\n🚀 Starting BCM validation...")
    result = validate_bcm_document(file_path)
    
    if result:
        print(f"\n✅ Validation completed successfully!")
        
        # Show final assessment
        compliance = result['compliance_score']
        if compliance >= 0.8:
            print(f"🟢 HIGH COMPLIANCE ({compliance:.1%}) - Good BCM framework")
        elif compliance >= 0.6:
            print(f"🟡 MEDIUM COMPLIANCE ({compliance:.1%}) - Needs improvement")
        else:
            print(f"🔴 LOW COMPLIANCE ({compliance:.1%}) - Significant gaps")
        
        print(f"\n💡 Next steps:")
        print(f"   1. Review the generated report")
        print(f"   2. Address missing requirements first")
        print(f"   3. Improve inadequate areas")
        print(f"   4. For advanced AI analysis, set up Ollama with Mistral model")
    else:
        print(f"\n❌ Validation failed")

if __name__ == "__main__":
    main()

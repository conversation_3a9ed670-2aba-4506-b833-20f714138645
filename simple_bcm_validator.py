"""
Simple BCM Validator - Based on your original approach but enhanced
This version uses Ollama for intelligent analysis instead of simple keyword matching.
"""

import ollama
from bcm_expert_chatbot.iso_standards import ISO22301Standards

# Your original BCM topics list
bcm_topics = """
Revision History
Information Technology Statement of Intent
Policy Statement
Business Impact Analysis
Risk Assessment
Recovery Strategies
Emergency Response Procedures
Communication Plans
Testing and Exercises
Training and Awareness
Vendor Management
Document Control
Incident Management
Crisis Management
Media Strategies
Media Team
Rules for Dealing with Media
Business Continuity Governance
Roles and Responsibilities
Resource Requirements
Alternate Site Procedures
Data Backup and Recovery
Supply Chain Management
Regulatory Compliance
Continuous Improvement
Management Review
Internal Audit
Performance Monitoring
"""

def evaluate_clause_with_ai(topic_list, iso_requirement):
    """Enhanced evaluation using AI instead of simple keyword matching"""
    
    prompt = f"""You are an expert ISO 22301 Business Continuity Management auditor.

Company's BCM Framework Topics:
\"\"\"
{topic_list}
\"\"\"

ISO 22301 Requirement to evaluate:
- Clause: {iso_requirement.clause}
- Title: {iso_requirement.title}
- Description: {iso_requirement.description}
- Key Elements Required: {', '.join(iso_requirement.key_elements)}

Analyze whether this ISO requirement is adequately covered in the company's BCM framework topics above.

Consider:
1. Are the key elements present in the topic list?
2. Is the coverage adequate for compliance?
3. What specific gaps exist?

Respond with ONLY:
STATUS: [Present and Adequate / Present but Inadequate / Missing]
CONFIDENCE: [0.0-1.0]
GAPS: [List specific missing elements]
RECOMMENDATIONS: [Specific actions needed]
"""

    try:
        response = ollama.chat(
            model="mistral", 
            messages=[{"role": "user", "content": prompt}],
            options={"temperature": 0.1}  # Low temperature for consistent analysis
        )
        return response['message']['content']
    except Exception as e:
        return f"Error: Unable to connect to Ollama. {e}"

def check_bcm_against_iso_enhanced():
    """Enhanced BCM validation using AI and complete ISO requirements"""
    
    print("🤖 Enhanced BCM Validation Against ISO 22301")
    print("=" * 60)
    
    try:
        # Initialize ISO standards
        iso_standards = ISO22301Standards()
        all_requirements = iso_standards.get_all_requirements()
        
        print(f"📋 Evaluating {len(all_requirements)} ISO 22301 requirements...")
        print(f"📄 Against your BCM framework topics\\n")
        
        # Results storage
        results = []
        status_counts = {"Present and Adequate": 0, "Present but Inadequate": 0, "Missing": 0}
        
        # Evaluate each requirement
        for clause, requirement in all_requirements.items():
            print(f"🔍 Checking {clause}: {requirement.title}")
            
            # Get AI analysis
            analysis = evaluate_clause_with_ai(bcm_topics, requirement)
            
            # Parse status (simple parsing)
            if "Present and Adequate" in analysis:
                status = "Present and Adequate"
            elif "Present but Inadequate" in analysis:
                status = "Present but Inadequate"
            else:
                status = "Missing"
            
            status_counts[status] += 1
            results.append({
                'clause': clause,
                'title': requirement.title,
                'status': status,
                'analysis': analysis
            })
            
            # Print status with emoji
            status_emoji = {
                "Present and Adequate": "✅",
                "Present but Inadequate": "⚠️", 
                "Missing": "❌"
            }
            print(f"   {status_emoji[status]} {status}\\n")
        
        # Print summary
        print("\\n" + "="*60)
        print("📊 VALIDATION SUMMARY")
        print("="*60)
        
        total = len(results)
        compliance_rate = (status_counts["Present and Adequate"] / total) * 100
        
        print(f"Overall Compliance Rate: {compliance_rate:.1f}%")
        print(f"\\n📈 Status Breakdown:")
        print(f"   ✅ Present and Adequate: {status_counts['Present and Adequate']} ({status_counts['Present and Adequate']/total*100:.1f}%)")
        print(f"   ⚠️ Present but Inadequate: {status_counts['Present but Inadequate']} ({status_counts['Present but Inadequate']/total*100:.1f}%)")
        print(f"   ❌ Missing: {status_counts['Missing']} ({status_counts['Missing']/total*100:.1f}%)")
        
        # Show critical gaps
        print(f"\\n🚨 CRITICAL GAPS (Missing Requirements):")
        missing_requirements = [r for r in results if r['status'] == 'Missing']
        for req in missing_requirements[:5]:  # Show top 5
            print(f"   • {req['clause']}: {req['title']}")
        
        # Show areas needing improvement
        print(f"\\n⚠️ AREAS NEEDING IMPROVEMENT (Inadequate):")
        inadequate_requirements = [r for r in results if r['status'] == 'Present but Inadequate']
        for req in inadequate_requirements[:5]:  # Show top 5
            print(f"   • {req['clause']}: {req['title']}")
        
        return results
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        print("\\nPlease ensure:")
        print("1. Ollama is running (ollama serve)")
        print("2. Mistral model is available (ollama pull mistral)")
        return None

def main():
    """Main function"""
    print("🤖 Simple BCM Validator - Enhanced with AI")
    print("Based on your original approach but using Ollama for intelligent analysis\\n")
    
    # Check if Ollama is available
    try:
        models = ollama.list()
        print(f"✅ Ollama is running with {len(models['models'])} models available")
        
        # Check for mistral model
        model_names = [model['name'] for model in models['models']]
        if any('mistral' in name for name in model_names):
            print("✅ Mistral model is available")
        else:
            print("❌ Mistral model not found. Run: ollama pull mistral")
            return
            
    except Exception as e:
        print(f"❌ Ollama not available: {e}")
        print("Please start Ollama service and ensure mistral model is installed")
        return
    
    print()
    
    # Run validation
    results = check_bcm_against_iso_enhanced()
    
    if results:
        print(f"\\n✅ Validation completed! Check the results above.")
        print(f"\\n💡 Next steps:")
        print(f"   1. Address missing requirements first")
        print(f"   2. Improve inadequate areas")
        print(f"   3. Use the full BCM Expert Chatbot for detailed guidance")

if __name__ == "__main__":
    main()

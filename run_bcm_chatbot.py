#!/usr/bin/env python3
"""
BCM Expert Chatbot Launcher
Simple launcher script that handles import issues and provides easy access to all features.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main launcher function"""
    print("🤖 BCM Expert Chatbot Launcher")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == "test":
        test_system()
    elif command == "validate":
        if len(sys.argv) < 3:
            print("❌ Please provide a file path to validate")
            print("Usage: python run_bcm_chatbot.py validate <file_path>")
            return
        validate_document(sys.argv[2])
    elif command == "chat":
        start_chat()
    elif command == "requirements":
        show_requirements()
    elif command == "simple":
        run_simple_validator()
    else:
        print_help()

def print_help():
    """Print help information"""
    print("""
Available commands:

🧪 test           - Test the system (check ISO standards)
📋 validate <file> - Validate a BCM document  
💬 chat           - Start interactive chatbot
📊 requirements   - Show ISO 22301 requirements
🔍 simple         - Run simple validator (your original approach enhanced)

Examples:
  python run_bcm_chatbot.py test
  python run_bcm_chatbot.py validate "Perpetuuiti_BCM_Plan.pdf"
  python run_bcm_chatbot.py chat
  python run_bcm_chatbot.py requirements
  python run_bcm_chatbot.py simple
""")

def test_system():
    """Test the system"""
    print("🧪 Testing BCM Expert Chatbot System...")
    
    try:
        from bcm_expert_chatbot.iso_standards import ISO22301Standards
        
        iso_standards = ISO22301Standards()
        all_requirements = iso_standards.get_all_requirements()
        
        print(f"✅ ISO Standards loaded: {len(all_requirements)} requirements")
        
        # Test Ollama connection
        try:
            import ollama
            models = ollama.list()
            print(f"✅ Ollama connected: {len(models['models'])} models available")
            
            # Check for required models
            model_names = [model['name'] for model in models['models']]
            if any('mistral' in name for name in model_names):
                print("✅ Mistral model available")
            else:
                print("⚠️ Mistral model not found. Run: ollama pull mistral")
                
            if any('nomic' in name for name in model_names):
                print("✅ Nomic embedding model available")
            else:
                print("⚠️ Nomic model not found. Run: ollama pull nomic-embed-text")
                
        except Exception as e:
            print(f"⚠️ Ollama not available: {e}")
            print("   Start Ollama service for full functionality")
        
        print("\\n✅ System test completed!")
        
    except Exception as e:
        print(f"❌ System test failed: {e}")

def validate_document(file_path):
    """Validate a document"""
    print(f"📋 Validating document: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    try:
        from bcm_expert_chatbot.validation_engine import BCMValidationEngine
        from bcm_expert_chatbot.report_generator import BCMReportGenerator
        
        # Initialize validator
        validator = BCMValidationEngine()
        
        # Validate document
        result = validator.validate_document(file_path)
        
        # Print results
        print(f"\\n✅ Validation completed!")
        print(f"📊 Overall Compliance: {result.overall_compliance:.1%}")
        print(f"⏱️ Processing Time: {result.processing_time:.2f}s")
        
        # Print summary
        summary = result.summary
        print(f"\\n📈 Status Breakdown:")
        print(f"   ✅ Compliant: {summary['status_counts']['COMPLIANT']}")
        print(f"   ⚠️ Partial: {summary['status_counts']['PARTIAL']}")
        print(f"   ❌ Non-Compliant: {summary['status_counts']['NON_COMPLIANT']}")
        print(f"   ❓ Not Found: {summary['status_counts']['NOT_FOUND']}")
        
        # Show top gaps
        if summary['critical_gaps']:
            print(f"\\n🚨 Top Critical Gaps:")
            for i, gap in enumerate(summary['critical_gaps'][:5], 1):
                print(f"   {i}. {gap}")
        
        # Generate report
        report_gen = BCMReportGenerator()
        exec_summary = report_gen.generate_executive_summary(result)
        
        report_file = f"bcm_validation_report_{result.validation_date.strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(exec_summary)
        
        print(f"\\n📄 Report saved: {report_file}")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        print("\\nTroubleshooting:")
        print("1. Ensure Ollama is running: ollama serve")
        print("2. Check models: ollama list")
        print("3. Install models: ollama pull mistral && ollama pull nomic-embed-text")

def start_chat():
    """Start interactive chat"""
    print("💬 Starting BCM Expert Chatbot...")
    
    try:
        from bcm_expert_chatbot.chatbot_interface import BCMExpertChatbot
        from datetime import datetime
        
        # Initialize chatbot
        chatbot = BCMExpertChatbot()
        
        # Create session
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        session = chatbot.create_session(session_id)
        
        # Print welcome message
        print("\\n" + session.messages[0].content)
        print("\\n" + "="*60 + "\\n")
        
        # Chat loop
        while True:
            try:
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("\\n👋 Thank you for using BCM Expert Chatbot!")
                    break
                
                if not user_input:
                    continue
                
                print("\\n🤖 BCM Expert:")
                response = chatbot.chat(session_id, user_input)
                print(response)
                print("\\n" + "-"*60 + "\\n")
                
            except KeyboardInterrupt:
                print("\\n\\n👋 Session ended.")
                break
            except Exception as e:
                print(f"\\n❌ Error: {e}")
                print("Please try again.\\n")
        
    except Exception as e:
        print(f"❌ Failed to start chatbot: {e}")
        print("\\nTroubleshooting:")
        print("1. Ensure Ollama is running: ollama serve")
        print("2. Check models: ollama list")

def show_requirements():
    """Show ISO requirements"""
    print("📊 ISO 22301:2019 Requirements")
    print("=" * 50)
    
    try:
        from bcm_expert_chatbot.iso_standards import ISO22301Standards
        
        iso_standards = ISO22301Standards()
        requirements_by_category = iso_standards.get_requirements_by_category()
        
        for category, reqs in requirements_by_category.items():
            print(f"\\n🔹 {category}:")
            for req in reqs:
                print(f"   {req.clause}: {req.title}")
        
        print(f"\\n📊 Total: {len(iso_standards.get_all_requirements())} requirements")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def run_simple_validator():
    """Run the simple validator"""
    print("🔍 Running Simple BCM Validator (Enhanced)...")
    
    try:
        # Import and run the simple validator
        import simple_bcm_validator
        simple_bcm_validator.main()
        
    except Exception as e:
        print(f"❌ Error running simple validator: {e}")
        print("\\nTroubleshooting:")
        print("1. Ensure Ollama is running: ollama serve")
        print("2. Check models: ollama list")

if __name__ == "__main__":
    main()

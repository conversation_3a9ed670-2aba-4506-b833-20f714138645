#!/usr/bin/env python3
"""
BCM Expert Chatbot - Simple Main File
Just run this file and provide your PDF path!
"""

import os
import sys
import time
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """Print application banner"""
    print("""
🤖 ===============================================
   BCM EXPERT CHATBOT
   AI-Powered Business Continuity Management
   ISO 22301 Validation System
===============================================
""")

def validate_bcm_document(file_path: str):
    """Validate BCM document against ISO 22301"""

    print(f"🔍 Starting BCM validation for: {os.path.basename(file_path)}")
    print("=" * 60)

    try:
        # Import required modules
        from bcm_expert_chatbot.validation_engine import BCMValidationEngine
        from bcm_expert_chatbot.report_generator import BCMReportGenerator

        print("🤖 Initializing BCM Validation Engine...")

        # Initialize validation engine
        validator = BCMValidationEngine()

        print("📄 Processing document...")

        # Validate document
        result = validator.validate_document(file_path, detailed_analysis=True)

        # Print results
        print(f"\\n✅ Validation completed!")
        print(f"📊 Overall Compliance: {result.overall_compliance:.1%}")
        print(f"⏱️ Processing Time: {result.processing_time:.2f}s")

        # Print status breakdown
        summary = result.summary
        print(f"\\n📈 Compliance Breakdown:")
        print(f"   ✅ Compliant: {summary['status_counts']['COMPLIANT']} requirements")
        print(f"   ⚠️ Partial: {summary['status_counts']['PARTIAL']} requirements")
        print(f"   ❌ Non-Compliant: {summary['status_counts']['NON_COMPLIANT']} requirements")
        print(f"   ❓ Not Found: {summary['status_counts']['NOT_FOUND']} requirements")

        # Show compliance percentages
        total_reqs = summary['total_requirements']
        print(f"\\n📊 Compliance Percentages:")
        print(f"   ✅ Compliant: {summary['compliance_percentage']['compliant']:.1f}%")
        print(f"   ⚠️ Partial: {summary['compliance_percentage']['partial']:.1f}%")
        print(f"   ❌ Non-Compliant: {summary['compliance_percentage']['non_compliant']:.1f}%")
        print(f"   ❓ Missing: {summary['compliance_percentage']['not_found']:.1f}%")

        # Show top critical gaps
        if summary['critical_gaps']:
            print(f"\\n🚨 TOP CRITICAL GAPS:")
            for i, gap in enumerate(summary['critical_gaps'][:10], 1):
                print(f"   {i}. {gap}")

        # Show top recommendations
        if summary['top_recommendations']:
            print(f"\\n💡 KEY RECOMMENDATIONS:")
            for i, rec in enumerate(summary['top_recommendations'][:10], 1):
                print(f"   {i}. {rec}")

        # Generate reports
        print(f"\\n📄 Generating reports...")

        # Create reports directory
        reports_dir = "bcm_reports"
        os.makedirs(reports_dir, exist_ok=True)

        # Initialize report generator
        report_gen = BCMReportGenerator()

        # Generate timestamp for filenames
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        doc_name = os.path.splitext(os.path.basename(file_path))[0]
        base_name = f"{doc_name}_validation_{timestamp}"

        # Generate executive summary
        exec_summary = report_gen.generate_executive_summary(result)
        exec_path = os.path.join(reports_dir, f"{base_name}_executive_summary.md")
        with open(exec_path, 'w', encoding='utf-8') as f:
            f.write(exec_summary)
        print(f"   ✅ Executive Summary: {exec_path}")

        # Generate detailed report
        detailed_report = report_gen.generate_detailed_report(result)
        detailed_path = os.path.join(reports_dir, f"{base_name}_detailed_report.md")
        with open(detailed_path, 'w', encoding='utf-8') as f:
            f.write(detailed_report)
        print(f"   ✅ Detailed Report: {detailed_path}")

        # Generate gap analysis
        gap_analysis = report_gen.generate_gap_analysis(result)
        gap_path = os.path.join(reports_dir, f"{base_name}_gap_analysis.md")
        with open(gap_path, 'w', encoding='utf-8') as f:
            f.write(gap_analysis)
        print(f"   ✅ Gap Analysis: {gap_path}")

        # Export JSON data
        json_path = os.path.join(reports_dir, f"{base_name}_data.json")
        report_gen.export_json(result, json_path)
        print(f"   ✅ JSON Export: {json_path}")

        print(f"\\n🎉 BCM validation completed successfully!")
        print(f"📁 All reports saved in: {reports_dir}/")

        # Show summary
        print(f"\\n📋 SUMMARY:")
        if result.overall_compliance >= 0.8:
            print(f"   🟢 HIGH COMPLIANCE ({result.overall_compliance:.1%}) - Good BCM framework")
        elif result.overall_compliance >= 0.6:
            print(f"   🟡 MEDIUM COMPLIANCE ({result.overall_compliance:.1%}) - Needs improvement")
        else:
            print(f"   🔴 LOW COMPLIANCE ({result.overall_compliance:.1%}) - Significant gaps")

        print(f"   📊 {summary['status_counts']['COMPLIANT']}/{total_reqs} requirements fully compliant")
        print(f"   ⚠️ {summary['status_counts']['PARTIAL'] + summary['status_counts']['NON_COMPLIANT']} requirements need attention")

        return result

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\\nTroubleshooting:")
        print("1. Ensure you're in the correct directory")
        print("2. Check that all required files are present")
        print("3. Install dependencies: pip install ollama PyPDF2 python-docx")
        return None

    except Exception as e:
        print(f"❌ Validation Error: {e}")
        print("\\nTroubleshooting:")
        print("1. Ensure Ollama is running: ollama serve")
        print("2. Check models are available: ollama list")
        print("3. Install models: ollama pull mistral && ollama pull nomic-embed-text")
        print("4. Check file format (PDF, DOCX, TXT supported)")
        return None

def main():
    """Main function"""
    print_banner()

    # Get file path
    if len(sys.argv) > 1:
        # File path provided as argument
        file_path = sys.argv[1]
        print(f"📄 Using provided file: {file_path}")
    else:
        # Auto-detect BCM PDF in current directory
        bcm_files = []
        for file in os.listdir('.'):
            if file.lower().endswith(('.pdf', '.docx', '.doc')):
                bcm_files.append(file)

        if bcm_files:
            print(f"📄 Found BCM documents in current directory:")
            for i, file in enumerate(bcm_files, 1):
                print(f"   {i}. {file}")

            if len(bcm_files) == 1:
                file_path = bcm_files[0]
                print(f"\n📄 Auto-selecting: {file_path}")
            else:
                print(f"\nSelect a file (1-{len(bcm_files)}) or enter custom path:")
                choice = input("Choice: ").strip()

                if choice.isdigit() and 1 <= int(choice) <= len(bcm_files):
                    file_path = bcm_files[int(choice) - 1]
                else:
                    file_path = choice.strip('"').strip("'")
        else:
            print("📄 No BCM documents found in current directory.")
            print("Please enter the path to your BCM document:")
            file_path = input("File path: ").strip().strip('"').strip("'")

    # Validate input
    if not file_path:
        print("❌ No file path provided")
        print("\\nUsage:")
        print("  python main.py <file_path>")
        print("  python main.py")
        return

    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        print("\\nPlease check:")
        print("1. File path is correct")
        print("2. File exists in the specified location")
        print("3. You have read permissions")
        return

    # Check file extension
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext not in ['.pdf', '.docx', '.doc', '.txt']:
        print(f"⚠️ Warning: Unsupported file type '{file_ext}'")
        print("Supported formats: PDF, DOCX, TXT")
        print("Attempting to process anyway...")

    # Process the document
    print(f"\\n🚀 Starting BCM validation...")
    result = validate_bcm_document(file_path)

    if result:
        print(f"\\n✅ Process completed successfully!")
        print(f"\\n💡 Next steps:")
        print(f"   1. Review the generated reports in 'bcm_reports/' folder")
        print(f"   2. Address the critical gaps identified")
        print(f"   3. Implement the recommended improvements")
        print(f"   4. Re-run validation after updates")
    else:
        print(f"\\n❌ Validation failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
